using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ZoomableApp.Models
{
    public class MaintenanceItem : INotifyPropertyChanged
    {
        private int _id;
        private string _content = "";
        private string _method = "";
        private int _cycle;
        private string _guide = "";
        private DateTime _lastMaintenanceDate;
        private DateTime _nextMaintenanceDate;
        private MaintenanceStatus _status;
        private string _result = "";
        private string _performedBy = "";

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public string Content
        {
            get => _content;
            set
            {
                _content = value;
                OnPropertyChanged();
            }
        }

        public string Method
        {
            get => _method;
            set
            {
                _method = value;
                OnPropertyChanged();
            }
        }

        public int Cycle
        {
            get => _cycle;
            set
            {
                _cycle = value;
                OnPropertyChanged();
                CalculateNextMaintenanceDate();
            }
        }

        public string Guide
        {
            get => _guide;
            set
            {
                _guide = value;
                OnPropertyChanged();
            }
        }

        public DateTime LastMaintenanceDate
        {
            get => _lastMaintenanceDate;
            set
            {
                _lastMaintenanceDate = value;
                OnPropertyChanged();
                CalculateNextMaintenanceDate();
            }
        }

        public DateTime NextMaintenanceDate
        {
            get => _nextMaintenanceDate;
            set
            {
                _nextMaintenanceDate = value;
                OnPropertyChanged();
                UpdateStatus();
            }
        }

        public MaintenanceStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public string Result
        {
            get => _result;
            set
            {
                _result = value;
                OnPropertyChanged();
            }
        }

        public string PerformedBy
        {
            get => _performedBy;
            set
            {
                _performedBy = value;
                OnPropertyChanged();
            }
        }

        public string LastMaintenanceDateText => LastMaintenanceDate.ToString("yyyy-MM-dd");
        public string NextMaintenanceDateText => NextMaintenanceDate.ToString("yyyy-MM-dd");

        private void CalculateNextMaintenanceDate()
        {
            if (LastMaintenanceDate != default && Cycle > 0)
            {
                NextMaintenanceDate = LastMaintenanceDate.AddDays(Cycle);
            }
        }

        private void UpdateStatus()
        {
            var today = DateTime.Today;
            if (NextMaintenanceDate <= today)
            {
                Status = MaintenanceStatus.Overdue;
            }
            else if (NextMaintenanceDate <= today.AddDays(7))
            {
                Status = MaintenanceStatus.DueSoon;
            }
            else
            {
                Status = MaintenanceStatus.Normal;
            }
        }

        public void CompleteMaintenance(string result, string performedBy)
        {
            LastMaintenanceDate = DateTime.Today;
            Result = result;
            PerformedBy = performedBy;
            Status = MaintenanceStatus.Normal;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum MaintenanceStatus
    {
        Normal,     // Bình thường
        DueSoon,    // Sắp đến hạn
        Overdue     // Quá hạn
    }
}
