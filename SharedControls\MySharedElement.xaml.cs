﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace ZoomableApp.SharedControls
{
    /// <summary>
    /// Interaction logic for MySharedElement.xaml
    /// </summary>
    public partial class MySharedElement : UserControl
    {
        public static readonly DependencyProperty ElementNameTextProperty =
            DependencyProperty.Register("ElementNameText", typeof(string), typeof(MySharedElement), new PropertyMetadata("Default Shared Element"));

        public string ElementNameText
        {
            get { return (string)GetValue(ElementNameTextProperty); }
            set { SetValue(ElementNameTextProperty, value); }
        }

        public MySharedElement()
        {
            InitializeComponent();
            // <PERSON><PERSON> tên cho UserControl gốc để Binding trong XAML hoạt động
            this.Name = "ucRoot";
        }
    }
}
