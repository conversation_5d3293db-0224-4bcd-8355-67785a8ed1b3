﻿<Window x:Class="ZoomableApp.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ZoomableApp"
        mc:Ignorable="d"
        Title="Đăng nhập hệ thống" Height="800" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        KeyDown="Window_KeyDown">

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20">
            <StackPanel>
                <TextBlock Text="HỆ THỐNG QUẢN LÝ SẢN XUẤT"
                           FontSize="18" FontWeight="Bold"
                           Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="Vui lòng đăng nhập để tiếp tục"
                           FontSize="12" Foreground="#BDC3C7"
                           HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="8"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid Margin="25">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Tab Control -->
                <TabControl Grid.Row="0" x:Name="LoginTabControl"
                           BorderThickness="0" Background="Transparent" MinHeight="300">

                    <!-- RFID Tab -->
                    <TabItem Header="Thẻ nhân viên" FontSize="14" Padding="15,8">
                        <Grid Margin="0,20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="Quét thẻ RFID của bạn"
                                       FontSize="16" FontWeight="SemiBold"
                                       HorizontalAlignment="Center" Margin="0,0,0,20"
                                       Foreground="#2C3E50"/>

                            <TextBlock Grid.Row="1" Text="Mã thẻ:" Margin="0,0,0,5"
                                       Foreground="#555" FontWeight="Medium"/>
                            <TextBox Grid.Row="2" x:Name="RfidCodeTextBox" Height="auto"
                                     FontSize="16" Padding="10" BorderBrush="#DDD"
                                     BorderThickness="2" VerticalContentAlignment="Center"
                                     Background="White" Foreground="Black"
                                     Text="" Margin="0,0,0,10"
                                     Tag="Nhập mã thẻ RFID hoặc quét thẻ..."/>

                            <!-- Shift Selection for RFID -->
                            <TextBlock Grid.Row="3" Text="Chọn ca làm việc:"
                                       Margin="0,15,0,5" Foreground="#555" FontWeight="Medium"/>
                            <ComboBox Grid.Row="4" x:Name="RfidShiftComboBox" Height="40"
                                      FontSize="14" Padding="10" BorderBrush="#DDD"
                                      BorderThickness="2" Background="White"
                                      Margin="0,0,0,10"/>
                        </Grid>
                    </TabItem>

                    <!-- Username/Password Tab -->
                    <TabItem Header="Tài khoản" FontSize="14" Padding="15,8">
                        <Grid Margin="0,20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="Đăng nhập bằng tài khoản"
                                       FontSize="16" FontWeight="SemiBold"
                                       HorizontalAlignment="Center" Margin="0,0,0,20"
                                       Foreground="#2C3E50"/>

                            <TextBlock Grid.Row="1" Text="Tên đăng nhập:"
                                       Margin="0,0,0,5" Foreground="#555" FontWeight="Medium"/>
                            <TextBox Grid.Row="2" x:Name="UsernameTextBox" Height="40"
                                     FontSize="14" Padding="10" BorderBrush="#DDD"
                                     BorderThickness="2" VerticalContentAlignment="Center"
                                     Background="White" Foreground="Black"
                                     Text="" Margin="0,0,0,10"
                                     Tag="Nhập tên đăng nhập..."/>

                            <TextBlock Grid.Row="3" Text="Mật khẩu:"
                                       Margin="0,10,0,5" Foreground="#555" FontWeight="Medium"/>
                            <PasswordBox Grid.Row="4" x:Name="PasswordBox" Height="40"
                                         FontSize="14" Padding="10" BorderBrush="#DDD"
                                         BorderThickness="2" VerticalContentAlignment="Center"
                                         Background="White" Foreground="Black"
                                         Margin="0,0,0,10"/>

                            <!-- Shift Selection for Account -->
                            <TextBlock Grid.Row="5" Text="Chọn ca làm việc:"
                                       Margin="0,15,0,5" Foreground="#555" FontWeight="Medium"/>
                            <ComboBox Grid.Row="6" x:Name="AccountShiftComboBox" Height="40"
                                      FontSize="14" Padding="10" BorderBrush="#DDD"
                                      BorderThickness="2" Background="White"
                                      Margin="0,0,0,10"/>
                        </Grid>
                    </TabItem>
                </TabControl>

                <!-- Status Message -->
                <TextBlock Grid.Row="1" x:Name="StatusTextBlock" Text=""
                           FontSize="13" Foreground="#E74C3C"
                           HorizontalAlignment="Center" Margin="0,10,0,10"
                           MinHeight="30" TextWrapping="Wrap" MaxWidth="350"
                           Background="LightYellow" Padding="5"
                           Visibility="Collapsed"/>

                <!-- Login Button -->
                <Button Grid.Row="2" x:Name="LoginButton" Content="ĐĂNG NHẬP"
                        Height="45" Margin="0,20,0,0" FontSize="14" FontWeight="Bold"
                        Background="#3498DB" Foreground="White" BorderThickness="0"
                        Click="LoginButton_Click" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#3498DB"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#2980B9"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#21618C"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#34495E" Padding="15">
            <TextBlock Text="© 2024 Hệ thống quản lý sản xuất - Phiên bản 1.0"
                       FontSize="11" Foreground="#95A5A6"
                       HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</Window>