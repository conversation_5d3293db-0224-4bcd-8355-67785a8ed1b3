﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Services
{
    public class PlcConnectionInfo
    {
        public string Id { get; set; }
        public string Name { get; set; } // Tên hiển thị
        public string IpAddress { get; set; }
        public int Port { get; set; }
        //public int LogicalStationNumber { get; set; }
        public bool AutoConnectOnStartup { get; set; } //Cờ để tự động kết nối
        public bool IsConnected { get; internal set; }
        public string ConnectionStatus { get; internal set; }
    }
}
