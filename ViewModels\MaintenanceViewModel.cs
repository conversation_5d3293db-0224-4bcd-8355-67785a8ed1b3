using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ZoomableApp.Models;
using ZoomableApp.Services;
using ZoomableApp.SharedControls;
using System.Windows;

namespace ZoomableApp.ViewModels
{
    public class MaintenanceViewModel : INotifyPropertyChanged
    {
        private readonly ExcelService _excelService;
        private readonly ExcelSettings _excelSettings;
        private ObservableCollection<MaintenanceItem> _maintenanceItems;
        private ObservableCollection<MaintenanceItem> _filteredItems;
        private MaintenanceItem _selectedItem;
        private DateTime _fromDate;
        private DateTime _toDate;
        private string _maintenanceResult = "";

        public MaintenanceViewModel()
        {
            _excelService = new ExcelService();
            _excelSettings = ConfigLoader.LoadExcelSettings();
            _maintenanceItems = new ObservableCollection<MaintenanceItem>();
            _filteredItems = new ObservableCollection<MaintenanceItem>();
            
            // Set default date range (current month)
            var today = DateTime.Today;
            _fromDate = new DateTime(today.Year, today.Month, 1);
            _toDate = today;

            // Commands
            RefreshCommand = new RelayCommand(LoadMaintenanceData);
            ViewHistoryCommand = new RelayCommand(ViewHistory);
            ExportExcelCommand = new RelayCommand(ExportToExcel);
            PerformMaintenanceCommand = new RelayCommand(PerformMaintenance, CanPerformMaintenance);

            // Load initial data
            LoadMaintenanceData();
        }

        #region Properties

        public ObservableCollection<MaintenanceItem> MaintenanceItems
        {
            get => _maintenanceItems;
            set
            {
                _maintenanceItems = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<MaintenanceItem> FilteredItems
        {
            get => _filteredItems;
            set
            {
                _filteredItems = value;
                OnPropertyChanged();
            }
        }

        public MaintenanceItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                OnPropertyChanged();
                // Trigger CanExecuteChanged for PerformMaintenanceCommand
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public DateTime FromDate
        {
            get => _fromDate;
            set
            {
                _fromDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime ToDate
        {
            get => _toDate;
            set
            {
                _toDate = value;
                OnPropertyChanged();
            }
        }

        public string MaintenanceResult
        {
            get => _maintenanceResult;
            set
            {
                _maintenanceResult = value;
                OnPropertyChanged();
                // Trigger CanExecuteChanged for PerformMaintenanceCommand
                CommandManager.InvalidateRequerySuggested();
            }
        }



        #endregion

        #region Commands

        public ICommand RefreshCommand { get; }
        public ICommand ViewHistoryCommand { get; }
        public ICommand ExportExcelCommand { get; }
        public ICommand PerformMaintenanceCommand { get; }

        #endregion

        #region Methods

        private void LoadMaintenanceData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: Loading maintenance data...");

                // Load from Excel file
                LoadFromExcel();

                // Apply current filter
                ApplyFilter();

                System.Diagnostics.Debug.WriteLine($"Maintenance: Loaded {MaintenanceItems.Count} items");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error loading data: {ex.Message}");
                // Fallback to sample data if Excel loading fails
                CreateSampleData();
                ApplyFilter();
            }
        }

        private void LoadFromExcel()
        {
            try
            {
                // Default path from config
                string excelPath = "maintainPlans.csv";

                // TODO: Load path from config file
                // var config = ConfigLoader.LoadExcelSettings();
                // if (!string.IsNullOrEmpty(config.MaintenancePlanPath))
                //     excelPath = config.MaintenancePlanPath;

                if (!System.IO.File.Exists(excelPath))
                {
                    System.Diagnostics.Debug.WriteLine($"Maintenance: Excel file not found at {excelPath}, using sample data");
                    CreateSampleData();
                    return;
                }

                var dataTable = ReadCsvFile(excelPath);
                if (dataTable != null && dataTable.Rows.Count > 0)
                {
                    MaintenanceItems.Clear();

                    for (int i = 0; i < dataTable.Rows.Count; i++)
                    {
                        var row = dataTable.Rows[i];
                        var item = new MaintenanceItem
                        {
                            Id = i + 1,
                            Content = row["Content"]?.ToString() ?? "",
                            Method = row["Method"]?.ToString() ?? "",
                            Cycle = int.TryParse(row["Cycle"]?.ToString(), out int cycle) ? cycle : 1,
                            Guide = row["Guide"]?.ToString() ?? "",
                            LastMaintenanceDate = DateTime.TryParse(row["Last"]?.ToString(), out DateTime lastDate) ? lastDate : DateTime.Today.AddDays(-1),
                            Result = row["MaintainResult"]?.ToString() ?? "",
                            PerformedBy = row["MaintainedBy"]?.ToString() ?? ""
                        };

                        // Calculate next maintenance date
                        item.NextMaintenanceDate = item.LastMaintenanceDate.AddMonths(item.Cycle);

                        MaintenanceItems.Add(item);
                    }
                }
                else
                {
                    CreateSampleData();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error loading Excel: {ex.Message}");
                CreateSampleData();
            }
        }

        private void CreateSampleData()
        {
            MaintenanceItems.Clear();
            
            var items = new[]
            {
                new MaintenanceItem
                {
                    Id = 1,
                    Content = "Bôi áp suất khí 0.4-0.6",
                    Method = "Quan sát đồng hồ áp suất",
                    Cycle = 1,
                    Guide = "Điều chỉnh áp suất khí nếu áp suất nằm ngoài thiết lập.",
                    LastMaintenanceDate = DateTime.Today.AddDays(-2),
                },
                new MaintenanceItem
                {
                    Id = 2,
                    Content = "Bạc bi trục trượt",
                    Method = "Dùng tay kiểm tra độ cứng, kết trục - Dùng mắt kiểm tra trục xước",
                    Cycle = 3,
                    Guide = "Kết trục bạc bi: Ngắt khí hệ thống, dùng tay nâng hạ cụm kiểm tra độ kết của trục",
                    LastMaintenanceDate = DateTime.Today.AddDays(-5),
                },
                new MaintenanceItem
                {
                    Id = 3,
                    Content = "Bu lông",
                    Method = "Dùng cờ lê bộ lục kiểm tra độ cứng của các vít bu lông",
                    Cycle = 1,
                    Guide = "Kết thêm đầu, mở ra đầu mỏ",
                    LastMaintenanceDate = DateTime.Today.AddDays(-1),
                },
                new MaintenanceItem
                {
                    Id = 4,
                    Content = "Gối bi, ổ bi",
                    Method = "Xịt RP7, Kiểm tra xem có tiếng động là khi vận hành hay không",
                    Cycle = 6,
                    Guide = "Kết thêm đầu, mở ra đầu mỏ, Kết độ hỏng bi xem nó thay thế",
                    LastMaintenanceDate = DateTime.Today.AddDays(-10),
                },
                new MaintenanceItem
                {
                    Id = 5,
                    Content = "Xy lanh",
                    Method = "Nghe xem có tiếng ồn không? Hoạt động có bình thường không?",
                    Cycle = 3,
                    Guide = "Tiếng ồn tiết lưu: Tiếng ồn phần đầu xy lanh xem nó thay phớt",
                    LastMaintenanceDate = DateTime.Today.AddDays(-8),
                },
                new MaintenanceItem
                {
                    Id = 6,
                    Content = "Đồng cơ",
                    Method = "Nghe xem có tiếng ồn không? Hoạt động có bình thường không?",
                    Cycle = 1,
                    Guide = "Kiểm tra phần cơ xem có bi bạc đạn có bi vỡ không; trục động cơ có bị bong không?",
                    LastMaintenanceDate = DateTime.Today.AddDays(-3),
                }
            };

            foreach (var item in items)
            {
                MaintenanceItems.Add(item);
            }
        }

        private void ApplyFilter()
        {
            FilteredItems.Clear();
            
            var filtered = MaintenanceItems.Where(item => 
                item.LastMaintenanceDate >= FromDate && 
                item.LastMaintenanceDate <= ToDate).ToList();
            
            foreach (var item in filtered)
            {
                FilteredItems.Add(item);
            }
        }

        private void ViewHistory()
        {
            System.Diagnostics.Debug.WriteLine($"Maintenance: Viewing history from {FromDate:yyyy-MM-dd} to {ToDate:yyyy-MM-dd}");
            ApplyFilter();
        }

        private void ExportToExcel()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: Exporting to Excel...");
                // TODO: Implement Excel export
                System.Diagnostics.Debug.WriteLine("Maintenance: Excel export completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error exporting to Excel: {ex.Message}");
            }
        }

        private bool CanPerformMaintenance()
        {
            bool canExecute = SelectedItem != null && !string.IsNullOrWhiteSpace(MaintenanceResult);
            System.Diagnostics.Debug.WriteLine($"Maintenance: CanPerformMaintenance = {canExecute}, SelectedItem = {SelectedItem?.Id}, MaintenanceResult = '{MaintenanceResult}'");
            return canExecute;
        }

        private void PerformMaintenance()
        {
            System.Diagnostics.Debug.WriteLine("Maintenance: PerformMaintenance called!");

            if (SelectedItem != null && !string.IsNullOrWhiteSpace(MaintenanceResult))
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"Maintenance: Processing maintenance for item {SelectedItem.Id}");

                    // Complete maintenance for selected item
                    SelectedItem.CompleteMaintenance(MaintenanceResult, "Current User"); // TODO: Get actual user

                    // Create new maintenance record for next cycle
                    var newItem = new MaintenanceItem
                    {
                        Id = MaintenanceItems.Count + 1,
                        Content = SelectedItem.Content,
                        Method = SelectedItem.Method,
                        Cycle = SelectedItem.Cycle,
                        Guide = SelectedItem.Guide,
                        LastMaintenanceDate = DateTime.Today,
                        NextMaintenanceDate = DateTime.Today.AddMonths(SelectedItem.Cycle),
                        Result = "",
                        PerformedBy = ""
                    };

                    MaintenanceItems.Add(newItem);

                    // Save back to Excel
                    SaveToExcel();

                    // Show success toast
                    ShowToast("Thành công", $"Đã hoàn thành bảo dưỡng: {SelectedItem.Content}", ToastType.Success);

                    // Clear input and refresh
                    MaintenanceResult = "";
                    ApplyFilter();

                    System.Diagnostics.Debug.WriteLine($"Maintenance: Successfully completed maintenance for item {SelectedItem.Id}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Maintenance: Error performing maintenance: {ex.Message}");
                    ShowToast("Lỗi", $"Không thể hoàn thành bảo dưỡng: {ex.Message}", ToastType.Error);
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: Cannot perform maintenance - missing data");
                ShowToast("Cảnh báo", "Vui lòng chọn mục bảo dưỡng và nhập kết quả", ToastType.Warning);
            }
        }

        private void ShowToast(string title, string message, ToastType type)
        {
            try
            {
                // Find the main window and show toast
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    mainWindow.ShowToast(title, message, type);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error showing toast: {ex.Message}");
            }
        }

        private void SaveToExcel()
        {
            try
            {
                // TODO: Implement Excel save functionality
                System.Diagnostics.Debug.WriteLine("Maintenance: Saving to Excel...");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error saving to Excel: {ex.Message}");
            }
        }

        private DataTable ReadCsvFile(string filePath)
        {
            var dataTable = new DataTable();

            try
            {
                if (!System.IO.File.Exists(filePath))
                    return dataTable;

                var lines = System.IO.File.ReadAllLines(filePath);
                if (lines.Length == 0)
                    return dataTable;

                // Read header
                var headers = lines[0].Split(',');
                foreach (var header in headers)
                {
                    dataTable.Columns.Add(header.Trim());
                }

                // Read data rows
                for (int i = 1; i < lines.Length; i++)
                {
                    var values = lines[i].Split(',');
                    var row = dataTable.NewRow();

                    for (int j = 0; j < Math.Min(values.Length, headers.Length); j++)
                    {
                        row[j] = values[j].Trim();
                    }

                    dataTable.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading CSV: {ex.Message}");
            }

            return dataTable;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }


}
