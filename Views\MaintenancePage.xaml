<UserControl x:Class="ZoomableApp.Views.MaintenancePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:viewmodels="clr-namespace:ZoomableApp.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <UserControl.DataContext>
        <viewmodels:MaintenanceViewModel />
    </UserControl.DataContext>
    
    <UserControl.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               CornerRadius="5" 
                               BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Status Color Converter -->
        <Style x:Key="StatusRowStyle" TargetType="DataGridRow">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Overdue">
                    <Setter Property="Background" Value="#E74C3C"/>
                    <Setter Property="Foreground" Value="White"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="DueSoon">
                    <Setter Property="Background" Value="#F39C12"/>
                    <Setter Property="Foreground" Value="White"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Normal">
                    <Setter Property="Background" Value="#2ECC71"/>
                    <Setter Property="Foreground" Value="White"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="#2C3E50">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header Section -->
        <Grid Grid.Row="0" Background="#34495E" Margin="10,10,10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Title -->
            <TextBlock Grid.Column="0" Text="LỊCH BẢO DƯỠNG" 
                      FontSize="20" FontWeight="Bold" 
                      Foreground="White" 
                      VerticalAlignment="Center" 
                      Margin="15,10"/>
            
            <!-- Export Button -->
            <Button Grid.Column="1" 
                   Content="Xuất Excel" 
                   Background="#8E44AD" 
                   Foreground="White" 
                   FontWeight="Bold"
                   Padding="15,8" 
                   Margin="10"
                   BorderThickness="0"
                   Command="{Binding ExportExcelCommand}"
                   Style="{StaticResource ModernButtonStyle}"/>
        </Grid>
        
        <!-- Control Panel -->
        <Grid Grid.Row="1" Background="#34495E" Margin="10,5,10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Tổng hợp Button -->
            <Button Grid.Column="0" 
                   Content="Tổng hợp" 
                   Background="#3498DB" 
                   Foreground="White" 
                   FontWeight="Bold"
                   Padding="12,6" 
                   Margin="10,8"
                   BorderThickness="0"
                   Command="{Binding RefreshCommand}"
                   Style="{StaticResource ModernButtonStyle}"/>
            
            <!-- Trạng thái Button -->
            <Button Grid.Column="1" 
                   Content="Trạng thái" 
                   Background="#9B59B6" 
                   Foreground="White" 
                   FontWeight="Bold"
                   Padding="12,6" 
                   Margin="5,8"
                   BorderThickness="0"
                   Style="{StaticResource ModernButtonStyle}"/>
            
            <!-- From Date -->
            <DatePicker Grid.Column="2" 
                       SelectedDate="{Binding FromDate}"
                       Margin="10,8,5,8"
                       VerticalAlignment="Center"
                       Width="120"/>
            
            <TextBlock Grid.Column="3" 
                      Text="Đến" 
                      Foreground="White" 
                      VerticalAlignment="Center" 
                      Margin="5,0"/>
            
            <!-- To Date -->
            <DatePicker Grid.Column="4" 
                       SelectedDate="{Binding ToDate}"
                       Margin="5,8,10,8"
                       VerticalAlignment="Center"
                       Width="120"/>
            
            <!-- Lịch sử Button -->
            <Button Grid.Column="5" 
                   Content="Lịch sử" 
                   Background="#3498DB" 
                   Foreground="White" 
                   FontWeight="Bold"
                   Padding="12,6" 
                   Margin="10,8"
                   BorderThickness="0"
                   Command="{Binding ViewHistoryCommand}"
                   Style="{StaticResource ModernButtonStyle}"/>
            
            <!-- Xác nhận Button -->
            <Button Grid.Column="7" 
                   Content="XÁC NHẬN" 
                   Background="#8E44AD" 
                   Foreground="White" 
                   FontWeight="Bold"
                   Padding="15,8" 
                   Margin="10,8"
                   BorderThickness="0"
                   Command="{Binding PerformMaintenanceCommand}"
                   Style="{StaticResource ModernButtonStyle}"/>
        </Grid>
        
        <!-- Main Content Area -->
        <Grid Grid.Row="2" Margin="10,5,10,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Left Panel - Maintenance Categories -->
            <Border Grid.Column="0" Background="#34495E" CornerRadius="5" Margin="0,0,5,0">
                <StackPanel Margin="15">
                    <TextBlock Text="MỤC BẢO DƯỠNG" 
                              FontSize="14" FontWeight="Bold" 
                              Foreground="White" 
                              Margin="0,0,0,10"/>
                    
                    <TextBlock Text="Bạc bi trục trượt" 
                              Foreground="White" 
                              Margin="0,5"/>
                    <TextBlock Text="- Dùng tay kiểm tra độ cứng kết trục" 
                              Foreground="#BDC3C7" 
                              FontSize="11"
                              Margin="10,2"/>
                    <TextBlock Text="- Dùng mắt kiểm tra trục xước" 
                              Foreground="#BDC3C7" 
                              FontSize="11"
                              Margin="10,2,10,10"/>
                    
                    <Rectangle Height="1" Fill="#7F8C8D" Margin="0,5"/>
                    
                    <TextBlock Text="KẾT QUẢ BẢO DƯỠNG" 
                              FontSize="14" FontWeight="Bold" 
                              Foreground="White" 
                              Margin="0,10,0,5"/>
                    
                    <TextBlock Text="(Nhấn vào đúng sắc nhận kết quả bảo dưỡng)" 
                              Foreground="#BDC3C7" 
                              FontSize="10"
                              TextWrapping="Wrap"
                              Margin="0,0,0,10"/>
                </StackPanel>
            </Border>
            
            <!-- Right Panel - Data Grid -->
            <Border Grid.Column="1" Background="#34495E" CornerRadius="5" Margin="5,0,0,0">
                <DataGrid ItemsSource="{Binding FilteredItems}"
                         SelectedItem="{Binding SelectedItem}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         Background="Transparent"
                         Foreground="Black"
                         RowStyle="{StaticResource StatusRowStyle}"
                         Margin="10">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="NoID" Binding="{Binding Id}" Width="50"/>
                        <DataGridTextColumn Header="Content" Binding="{Binding Content}" Width="150"/>
                        <DataGridTextColumn Header="Method" Binding="{Binding Method}" Width="200"/>
                        <DataGridTextColumn Header="Cycle" Binding="{Binding Cycle}" Width="60"/>
                        <DataGridTextColumn Header="Guide" Binding="{Binding Guide}" Width="200"/>
                        <DataGridTextColumn Header="Last" Binding="{Binding LastMaintenanceDateText}" Width="100"/>
                        <DataGridTextColumn Header="Plan" Binding="{Binding NextMaintenanceDateText}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>
        
        <!-- Maintenance Dialog Overlay -->
        <Grid Grid.RowSpan="3" 
              Background="#80000000" 
              Visibility="{Binding IsMaintenanceDialogOpen, Converter={StaticResource BooleanToVisibilityConverter}}"
              HorizontalAlignment="Stretch" 
              VerticalAlignment="Stretch">
            
            <Border Background="#34495E" 
                   CornerRadius="10" 
                   Width="400" 
                   Height="250"
                   HorizontalAlignment="Center" 
                   VerticalAlignment="Center">
                
                <StackPanel Margin="20">
                    <TextBlock Text="NHẬP KẾT QUẢ BẢO DƯỠNG" 
                              FontSize="16" FontWeight="Bold" 
                              Foreground="White" 
                              HorizontalAlignment="Center"
                              Margin="0,0,0,20"/>
                    
                    <TextBlock Text="Kết quả:" 
                              Foreground="White" 
                              Margin="0,0,0,5"/>
                    
                    <TextBox Text="{Binding MaintenanceResult}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,20"/>
                    
                    <StackPanel Orientation="Horizontal" 
                               HorizontalAlignment="Center">
                        <Button Content="Xác nhận" 
                               Background="#27AE60" 
                               Foreground="White" 
                               FontWeight="Bold"
                               Padding="20,8" 
                               Margin="0,0,10,0"
                               BorderThickness="0"
                               Command="{Binding ConfirmMaintenanceCommand}"
                               Style="{StaticResource ModernButtonStyle}"/>
                        
                        <Button Content="Hủy" 
                               Background="#E74C3C" 
                               Foreground="White" 
                               FontWeight="Bold"
                               Padding="20,8" 
                               Margin="10,0,0,0"
                               BorderThickness="0"
                               Command="{Binding CancelMaintenanceCommand}"
                               Style="{StaticResource ModernButtonStyle}"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</UserControl>
