using System;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.IO;
using System.Windows.Input;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    public class PlanViewModel : INotifyPropertyChanged
    {
        private readonly ExcelService _excelService;
        private readonly ExcelSettings _excelSettings;
        private DataTable _monthlyPlanData;
        private DataTable _dailyPlanData;
        private string _excelFilePath = "";
        private string _excelFileStatus = "";
        private string _excelRowCount = "";
        private string _lastUpdated = "";
        private string _planStatus = "";
        private string _todayDateText = "";

        public PlanViewModel()
        {
            _excelService = new ExcelService();
            _excelSettings = ConfigLoader.LoadExcelSettings();
            
            RefreshCommand = new RelayCommand(LoadExcelData);

            // Set today date text
            TodayDateText = $"({DateTime.Now:dd/MM/yyyy})";

            // Auto-load khi khởi tạo
            LoadExcelData();
        }

        #region Properties

        public DataTable MonthlyPlanData
        {
            get => _monthlyPlanData;
            set
            {
                _monthlyPlanData = value;
                OnPropertyChanged();
            }
        }

        public DataTable DailyPlanData
        {
            get => _dailyPlanData;
            set
            {
                _dailyPlanData = value;
                OnPropertyChanged();
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                _todayDateText = value;
                OnPropertyChanged();
            }
        }

        public string ExcelFilePath
        {
            get => _excelFilePath;
            set
            {
                _excelFilePath = value;
                OnPropertyChanged(nameof(ExcelFilePath));
            }
        }

        public string ExcelFileStatus
        {
            get => _excelFileStatus;
            set
            {
                _excelFileStatus = value;
                OnPropertyChanged(nameof(ExcelFileStatus));
            }
        }

        public string ExcelRowCount
        {
            get => _excelRowCount;
            set
            {
                _excelRowCount = value;
                OnPropertyChanged(nameof(ExcelRowCount));
            }
        }

        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        public string PlanStatus
        {
            get => _planStatus;
            set
            {
                _planStatus = value;
                OnPropertyChanged(nameof(PlanStatus));
            }
        }

        #endregion

        #region Commands

        public ICommand RefreshCommand { get; }

        #endregion

        #region Methods

        private void LoadExcelData()
        {
            try
            {
                PlanStatus = "Đang tải dữ liệu Excel...";
                
                // Hiển thị thông tin file
                ExcelFilePath = Path.GetFileName(_excelSettings.PlanFilePath);
                
                // Kiểm tra file tồn tại
                if (!_excelService.IsValidExcelFile(_excelSettings.PlanFilePath))
                {
                    ExcelFileStatus = "Trạng thái: File không tồn tại hoặc không hợp lệ";
                    ExcelRowCount = "0 dòng";
                    PlanStatus = $"Lỗi: File không tồn tại - {_excelSettings.PlanFilePath}";
                    MonthlyPlanData = new DataTable(); // Empty table
                    DailyPlanData = new DataTable(); // Empty table
                    return;
                }

                // Load dữ liệu từ Excel
                DataTable excelData = _excelService.ReadExcelToDataTable(_excelSettings.PlanFilePath);

                // Set monthly plan data (toàn bộ dữ liệu)
                MonthlyPlanData = excelData;

                // Extract daily plan data (dữ liệu của ngày hôm nay)
                ExtractDailyPlan(excelData);

                // Cập nhật thông tin
                ExcelFileStatus = "Trạng thái: Đã tải thành công";
                ExcelRowCount = $"{excelData.Rows.Count} dòng";
                LastUpdated = $"Cập nhật: {DateTime.Now:HH:mm:ss}";

                var dailyCount = DailyPlanData?.Rows.Count ?? 0;
                PlanStatus = $"Tháng: {excelData.Rows.Count} dòng | Hôm nay: {dailyCount} dòng";
                
                System.Diagnostics.Debug.WriteLine($"Excel loaded: {excelData.Rows.Count} rows, {excelData.Columns.Count} columns");
            }
            catch (Exception ex)
            {
                ExcelFileStatus = "Trạng thái: Lỗi khi tải";
                ExcelRowCount = "0 dòng";
                PlanStatus = $"Lỗi khi tải Excel: {ex.Message}";
                MonthlyPlanData = new DataTable(); // Empty table
                DailyPlanData = new DataTable(); // Empty table

                System.Diagnostics.Debug.WriteLine($"Error loading Excel: {ex.Message}");
            }
        }

        private void ExtractDailyPlan(DataTable monthlyData)
        {
            try
            {
                if (monthlyData == null || monthlyData.Rows.Count == 0)
                {
                    DailyPlanData = new DataTable();
                    return;
                }

                // Tìm cột Date
                DataColumn dateColumn = null;
                foreach (DataColumn column in monthlyData.Columns)
                {
                    if (column.ColumnName.ToLower().Contains("date") ||
                        column.ColumnName.ToLower().Contains("ngày"))
                    {
                        dateColumn = column;
                        break;
                    }
                }

                if (dateColumn == null)
                {
                    // Nếu không tìm thấy cột Date, tạo empty table
                    DailyPlanData = new DataTable();
                    return;
                }

                // Tạo DataTable mới với cùng structure
                DataTable dailyTable = monthlyData.Clone();

                // Lấy ngày hôm nay
                string today = DateTime.Now.ToString("dd/MM");
                string todayFull = DateTime.Now.ToString("dd/MM/yyyy");

                // Lọc các dòng có ngày trùng với hôm nay
                foreach (DataRow row in monthlyData.Rows)
                {
                    string dateValue = row[dateColumn].ToString();

                    // Kiểm tra nhiều format ngày
                    if (!string.IsNullOrEmpty(dateValue))
                    {
                        // Thử parse với nhiều format
                        if (IsDateMatch(dateValue, today, todayFull))
                        {
                            dailyTable.ImportRow(row);
                        }
                    }
                }

                DailyPlanData = dailyTable;

                System.Diagnostics.Debug.WriteLine($"Daily plan extracted: {dailyTable.Rows.Count} rows for today ({today})");
            }
            catch (Exception ex)
            {
                DailyPlanData = new DataTable();
                System.Diagnostics.Debug.WriteLine($"Error extracting daily plan: {ex.Message}");
            }
        }

        private bool IsDateMatch(string dateValue, string today, string todayFull)
        {
            try
            {
                // Thử parse với format dd/MM/yyyy
                if (DateTime.TryParseExact(dateValue, "dd/MM/yyyy", null, DateTimeStyles.None, out DateTime parsedDate))
                {
                    return parsedDate.ToString("dd/MM") == today;
                }

                // Thử parse với format dd/MM
                if (DateTime.TryParseExact($"{dateValue}/{DateTime.Now.Year}", "dd/MM/yyyy", null, DateTimeStyles.None, out parsedDate))
                {
                    return parsedDate.ToString("dd/MM") == today;
                }

                // Thử parse với format khác
                if (DateTime.TryParse(dateValue, out parsedDate))
                {
                    return parsedDate.ToString("dd/MM") == today;
                }

                // Kiểm tra string trực tiếp
                return dateValue.Contains(today) || dateValue.Contains(todayFull);
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    // RelayCommand helper class
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object parameter) => _execute();
    }
}
