using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ZoomableApp.Models;
using ZoomableApp.Services;
using ZoomableApp.SharedControls;
using System.Windows;
using System.Windows.Threading;

namespace ZoomableApp.ViewModels
{
    public class MaintenanceViewModel : INotifyPropertyChanged
    {
        private readonly ExcelService _excelService;
        private readonly ExcelSettings _excelSettings;
        private ObservableCollection<MaintenanceItem> _maintenanceItems;
        private ObservableCollection<MaintenanceItem> _filteredItems;
        private MaintenanceItem _selectedItem;
        private DateTime _fromDate;
        private DateTime _toDate;
        private string _maintenanceResult = "";
        private string _excelFilePath = "";
        private string _excelFileStatus = "";
        private DispatcherTimer _warningTimer;

        public MaintenanceViewModel()
        {
            _excelService = new ExcelService();
            _excelSettings = ConfigLoader.LoadExcelSettings();
            _maintenanceItems = new ObservableCollection<MaintenanceItem>();
            _filteredItems = new ObservableCollection<MaintenanceItem>();
            
            // Set default date range - wider range to show all data
            var today = DateTime.Today;
            _fromDate = today.AddYears(-1);
            _toDate = today.AddYears(1);

            // Commands
            RefreshCommand = new RelayCommand(LoadMaintenanceData);
            ViewHistoryCommand = new RelayCommand(ViewHistory);
            ExportExcelCommand = new RelayCommand(ExportToExcel);
            PerformMaintenanceCommand = new RelayCommand(PerformMaintenance, CanPerformMaintenance);
            ShowAllCommand = new RelayCommand(ShowAllData);
            ShowCurrentMonthCommand = new RelayCommand(ShowCurrentMonthData);

            // Load initial data
            LoadMaintenanceData();

            // Start maintenance warning timer
            StartMaintenanceWarningTimer();
        }

        #region Properties

        public ObservableCollection<MaintenanceItem> MaintenanceItems
        {
            get => _maintenanceItems;
            set
            {
                _maintenanceItems = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<MaintenanceItem> FilteredItems
        {
            get => _filteredItems;
            set
            {
                _filteredItems = value;
                OnPropertyChanged();
            }
        }

        public MaintenanceItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                OnPropertyChanged();
                // Trigger CanExecuteChanged for PerformMaintenanceCommand
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public DateTime FromDate
        {
            get => _fromDate;
            set
            {
                _fromDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime ToDate
        {
            get => _toDate;
            set
            {
                _toDate = value;
                OnPropertyChanged();
            }
        }

        public string MaintenanceResult
        {
            get => _maintenanceResult;
            set
            {
                _maintenanceResult = value;
                OnPropertyChanged();
                // Trigger CanExecuteChanged for PerformMaintenanceCommand
                CommandManager.InvalidateRequerySuggested();
            }
        }

        public string ExcelFilePath
        {
            get => _excelFilePath;
            set
            {
                _excelFilePath = value;
                OnPropertyChanged();
            }
        }

        public string ExcelFileStatus
        {
            get => _excelFileStatus;
            set
            {
                _excelFileStatus = value;
                OnPropertyChanged();
            }
        }



        #endregion

        #region Commands

        public ICommand RefreshCommand { get; }
        public ICommand ViewHistoryCommand { get; }
        public ICommand ExportExcelCommand { get; }
        public ICommand PerformMaintenanceCommand { get; }
        public ICommand ShowAllCommand { get; }
        public ICommand ShowCurrentMonthCommand { get; }

        #endregion

        #region Methods

        private void LoadMaintenanceData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: Loading maintenance data...");

                // Load from Excel file
                LoadFromExcel();

                // Apply current filter
                ApplyFilter();

                System.Diagnostics.Debug.WriteLine($"Maintenance: Loaded {MaintenanceItems.Count} items");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error loading data: {ex.Message}");
                // Fallback to sample data if Excel loading fails
                CreateSampleData();
                ApplyFilter();
            }
        }

        private void LoadFromExcel()
        {
            try
            {
                // Hiển thị thông tin file
                ExcelFilePath = Path.GetFileName(_excelSettings.MaintenancePlanPath);

                // Kiểm tra file tồn tại
                if (!_excelService.IsValidExcelFile(_excelSettings.MaintenancePlanPath))
                {
                    ExcelFileStatus = "Trạng thái: File không tồn tại hoặc không hợp lệ";
                    System.Diagnostics.Debug.WriteLine($"Maintenance: Excel file not found at {_excelSettings.MaintenancePlanPath}, using sample data");
                    CreateSampleData();
                    return;
                }

                // Load dữ liệu từ Excel
                DataTable excelData = _excelService.ReadExcelToDataTable(_excelSettings.MaintenancePlanPath);

                if (excelData != null && excelData.Rows.Count > 0)
                {
                    ExcelFileStatus = $"Trạng thái: Đã tải {excelData.Rows.Count} bản ghi";
                    MaintenanceItems.Clear();

                    for (int i = 0; i < excelData.Rows.Count; i++)
                    {
                        var row = excelData.Rows[i];
                        var item = new MaintenanceItem
                        {
                            Id = i + 1,
                            Content = row["Content"]?.ToString() ?? "",
                            Method = row["Method"]?.ToString() ?? "",
                            Cycle = int.TryParse(row["Cycle"]?.ToString(), out int cycle) ? cycle : 1,
                            Guide = row["Guide"]?.ToString() ?? "",
                            LastMaintenanceDate = DateTime.TryParse(row["Last"]?.ToString(), out DateTime lastDate) ? lastDate : DateTime.Today.AddDays(-1),
                            Result = row["MaintainResult"]?.ToString() ?? "",
                            PerformedBy = row["MaintainedBy"]?.ToString() ?? ""
                        };

                        // Calculate next maintenance date
                        item.NextMaintenanceDate = item.LastMaintenanceDate.AddMonths(item.Cycle);

                        MaintenanceItems.Add(item);
                    }
                }
                else
                {
                    ExcelFileStatus = "Trạng thái: File rỗng";
                    CreateSampleData();
                }
            }
            catch (Exception ex)
            {
                ExcelFileStatus = $"Trạng thái: Lỗi - {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error loading Excel: {ex.Message}");
                CreateSampleData();
            }
        }

        private void CreateSampleData()
        {
            MaintenanceItems.Clear();
            
            var items = new[]
            {
                new MaintenanceItem
                {
                    Id = 1,
                    Content = "Bôi áp suất khí 0.4-0.6",
                    Method = "Quan sát đồng hồ áp suất",
                    Cycle = 1,
                    Guide = "Điều chỉnh áp suất khí nếu áp suất nằm ngoài thiết lập.",
                    LastMaintenanceDate = DateTime.Today.AddDays(-2),
                },
                new MaintenanceItem
                {
                    Id = 2,
                    Content = "Bạc bi trục trượt",
                    Method = "Dùng tay kiểm tra độ cứng, kết trục - Dùng mắt kiểm tra trục xước",
                    Cycle = 3,
                    Guide = "Kết trục bạc bi: Ngắt khí hệ thống, dùng tay nâng hạ cụm kiểm tra độ kết của trục",
                    LastMaintenanceDate = DateTime.Today.AddDays(-5),
                },
                new MaintenanceItem
                {
                    Id = 3,
                    Content = "Bu lông",
                    Method = "Dùng cờ lê bộ lục kiểm tra độ cứng của các vít bu lông",
                    Cycle = 1,
                    Guide = "Kết thêm đầu, mở ra đầu mỏ",
                    LastMaintenanceDate = DateTime.Today.AddDays(-1),
                },
                new MaintenanceItem
                {
                    Id = 4,
                    Content = "Gối bi, ổ bi",
                    Method = "Xịt RP7, Kiểm tra xem có tiếng động là khi vận hành hay không",
                    Cycle = 6,
                    Guide = "Kết thêm đầu, mở ra đầu mỏ, Kết độ hỏng bi xem nó thay thế",
                    LastMaintenanceDate = DateTime.Today.AddDays(-10),
                },
                new MaintenanceItem
                {
                    Id = 5,
                    Content = "Xy lanh",
                    Method = "Nghe xem có tiếng ồn không? Hoạt động có bình thường không?",
                    Cycle = 3,
                    Guide = "Tiếng ồn tiết lưu: Tiếng ồn phần đầu xy lanh xem nó thay phớt",
                    LastMaintenanceDate = DateTime.Today.AddDays(-8),
                },
                new MaintenanceItem
                {
                    Id = 6,
                    Content = "Đồng cơ",
                    Method = "Nghe xem có tiếng ồn không? Hoạt động có bình thường không?",
                    Cycle = 1,
                    Guide = "Kiểm tra phần cơ xem có bi bạc đạn có bi vỡ không; trục động cơ có bị bong không?",
                    LastMaintenanceDate = DateTime.Today.AddDays(-3),
                }
            };

            foreach (var item in items)
            {
                MaintenanceItems.Add(item);
            }
        }

        private void ApplyFilter()
        {
            FilteredItems.Clear();

            System.Diagnostics.Debug.WriteLine($"Maintenance: ApplyFilter - MaintenanceItems.Count = {MaintenanceItems.Count}");
            System.Diagnostics.Debug.WriteLine($"Maintenance: ApplyFilter - FromDate = {FromDate:yyyy-MM-dd}, ToDate = {ToDate:yyyy-MM-dd}");

            var filtered = MaintenanceItems.Where(item =>
                item.NextMaintenanceDate >= FromDate &&
                item.NextMaintenanceDate <= ToDate).ToList();

            System.Diagnostics.Debug.WriteLine($"Maintenance: ApplyFilter - Filtered count = {filtered.Count}");

            foreach (var item in filtered)
            {
                FilteredItems.Add(item);
                System.Diagnostics.Debug.WriteLine($"Maintenance: Added to FilteredItems - {item.Content} (LastDate: {item.LastMaintenanceDate:yyyy-MM-dd})");
            }

            System.Diagnostics.Debug.WriteLine($"Maintenance: ApplyFilter - FilteredItems.Count = {FilteredItems.Count}");
        }

        private void ViewHistory()
        {
            System.Diagnostics.Debug.WriteLine($"Maintenance: Viewing history from {FromDate:yyyy-MM-dd} to {ToDate:yyyy-MM-dd}");
            ApplyFilter();
        }

        private void ShowAllData()
        {
            System.Diagnostics.Debug.WriteLine("Maintenance: Showing all data");

            // Set wide date range to show all data
            FromDate = DateTime.Today.AddYears(-5);
            ToDate = DateTime.Today.AddYears(5);

            ApplyFilter();
            ShowToast("Tổng hợp", "Hiển thị tất cả dữ liệu bảo dưỡng", ToastType.Info);
        }

        private void ShowCurrentMonthData()
        {
            System.Diagnostics.Debug.WriteLine("Maintenance: Showing current month data");

            // Set date range to current month
            var today = DateTime.Today;
            FromDate = new DateTime(today.Year, today.Month, 1);
            ToDate = FromDate.AddMonths(1).AddDays(-1);

            ApplyFilter();
            ShowToast("Trong tháng", $"Hiển thị dữ liệu tháng {today.Month}/{today.Year}", ToastType.Info);
        }

        private void ExportToExcel()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: Exporting filtered data to Excel...");

                // Tạo DataTable từ FilteredItems (dữ liệu hiện tại đang hiển thị)
                var dataTable = new DataTable();
                dataTable.Columns.Add("Content", typeof(string));
                dataTable.Columns.Add("Method", typeof(string));
                dataTable.Columns.Add("Cycle", typeof(int));
                dataTable.Columns.Add("Guide", typeof(string));
                dataTable.Columns.Add("Last Maintenance", typeof(string));
                dataTable.Columns.Add("Next Maintenance", typeof(string));
                dataTable.Columns.Add("Result", typeof(string));
                dataTable.Columns.Add("Performed By", typeof(string));

                // Thêm dữ liệu từ FilteredItems
                foreach (var item in FilteredItems)
                {
                    var row = dataTable.NewRow();
                    row["Content"] = item.Content;
                    row["Method"] = item.Method;
                    row["Cycle"] = item.Cycle;
                    row["Guide"] = item.Guide;
                    row["Last Maintenance"] = item.LastMaintenanceDate.ToString("MM/dd/yyyy");
                    row["Next Maintenance"] = item.NextMaintenanceDate.ToString("MM/dd/yyyy");
                    row["Result"] = item.Result;
                    row["Performed By"] = item.PerformedBy;
                    dataTable.Rows.Add(row);
                }

                // Tạo tên file với timestamp
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var exportFileName = $"MaintenanceExport_{timestamp}.xlsx";
                var exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), exportFileName);

                // Xuất ra file Excel
                _excelService.WriteDataTableToExcel(dataTable, exportPath, "Maintenance Export", true);

                ShowToast("Thành công", $"Đã xuất {dataTable.Rows.Count} bản ghi ra file: {exportFileName}", ToastType.Success);
                System.Diagnostics.Debug.WriteLine($"Maintenance: Successfully exported {dataTable.Rows.Count} records to {exportPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error exporting to Excel: {ex.Message}");
                ShowToast("Lỗi", $"Không thể xuất Excel: {ex.Message}", ToastType.Error);
            }
        }

        private bool CanPerformMaintenance()
        {
            // Always allow the button to be clickable, we'll handle validation in the execute method
            return true;
        }

        private void PerformMaintenance()
        {
            System.Diagnostics.Debug.WriteLine("Maintenance: PerformMaintenance called!");

            // Validation: Check if item is selected
            if (SelectedItem == null)
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: No item selected");
                ShowToast("Cảnh báo", "Vui lòng chọn một mục bảo dưỡng trước khi xác nhận", ToastType.Warning);
                return;
            }

            // Validation: Check if maintenance result is provided
            if (string.IsNullOrWhiteSpace(MaintenanceResult))
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: No maintenance result provided");
                ShowToast("Cảnh báo", "Vui lòng nhập kết quả bảo dưỡng trước khi xác nhận", ToastType.Warning);
                return;
            }

            try
            {
                // Store all needed information before any operations that might change SelectedItem
                int selectedItemId = SelectedItem.Id;
                string completedContent = SelectedItem.Content;
                string selectedMethod = SelectedItem.Method;
                int selectedCycle = SelectedItem.Cycle;
                string selectedGuide = SelectedItem.Guide;
                bool wasAlreadyMaintained = !string.IsNullOrEmpty(SelectedItem.PerformedBy);

                System.Diagnostics.Debug.WriteLine($"Maintenance: Processing maintenance for item {selectedItemId}");
                System.Diagnostics.Debug.WriteLine($"Maintenance: Item was already maintained: {wasAlreadyMaintained}");

                // Complete maintenance for selected item (update result and performed by)
                string performedBy = UserSession.CurrentUser?.Fullname ?? "Unknown User";
                SelectedItem.CompleteMaintenance(MaintenanceResult.Trim(), performedBy);

                // Only create new maintenance record if this item wasn't already maintained
                if (!wasAlreadyMaintained)
                {
                    System.Diagnostics.Debug.WriteLine("Maintenance: Creating new maintenance plan for next cycle");

                    var newItem = new MaintenanceItem
                    {
                        Id = MaintenanceItems.Count + 1,
                        Content = completedContent,
                        Method = selectedMethod,
                        Cycle = selectedCycle,
                        Guide = selectedGuide,
                        LastMaintenanceDate = DateTime.Today,
                        NextMaintenanceDate = DateTime.Today.AddMonths(selectedCycle),
                        Result = "",
                        PerformedBy = ""
                    };

                    MaintenanceItems.Add(newItem);
                    System.Diagnostics.Debug.WriteLine($"Maintenance: Added new maintenance plan with ID {newItem.Id}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Maintenance: Item was already maintained, only updating result");
                }

                // Save back to Excel
                SaveToExcel();

                // Show success toast
                string action = wasAlreadyMaintained ? "Đã cập nhật kết quả bảo dưỡng" : "Đã hoàn thành bảo dưỡng";
                ShowToast("Thành công", $"{action}: {completedContent}", ToastType.Success);

                // Clear input and refresh
                MaintenanceResult = "";
                ApplyFilter();

                System.Diagnostics.Debug.WriteLine($"Maintenance: Successfully processed maintenance for item {selectedItemId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error performing maintenance: {ex.Message}");
                ShowToast("Lỗi", $"Không thể hoàn thành bảo dưỡng: {ex.Message}", ToastType.Error);
            }
        }

        private void ShowToast(string title, string message, ToastType type)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: ShowToast called - Title: '{title}', Message: '{message}', Type: {type}");

                // Use ToastNotificationPanel.Instance directly
                Application.Current.Dispatcher.Invoke(() =>
                {
                    ToastNotificationPanel.Instance.Show($"{title}: {message}", type);
                });

                System.Diagnostics.Debug.WriteLine($"Maintenance: Toast notification sent successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error showing toast: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Maintenance: Stack trace: {ex.StackTrace}");
            }
        }

        private void SaveToExcel()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Maintenance: Saving to Excel...");

                // Tạo DataTable từ MaintenanceItems
                var dataTable = new DataTable();
                dataTable.Columns.Add("Content", typeof(string));
                dataTable.Columns.Add("Method", typeof(string));
                dataTable.Columns.Add("Cycle", typeof(int));
                dataTable.Columns.Add("Guide", typeof(string));
                dataTable.Columns.Add("Last", typeof(string));
                dataTable.Columns.Add("MaintainResult", typeof(string));
                dataTable.Columns.Add("MaintainedBy", typeof(string));

                // Thêm dữ liệu từ MaintenanceItems
                foreach (var item in MaintenanceItems)
                {
                    var row = dataTable.NewRow();
                    row["Content"] = item.Content;
                    row["Method"] = item.Method;
                    row["Cycle"] = item.Cycle;
                    row["Guide"] = item.Guide;
                    row["Last"] = item.LastMaintenanceDate.ToString("MM/dd/yyyy");
                    row["MaintainResult"] = item.Result;
                    row["MaintainedBy"] = item.PerformedBy;
                    dataTable.Rows.Add(row);
                }

                // Lưu vào file Excel
                _excelService.WriteDataTableToExcel(dataTable, _excelSettings.MaintenancePlanPath, "MaintenanceData", true);

                System.Diagnostics.Debug.WriteLine($"Maintenance: Successfully saved {dataTable.Rows.Count} records to Excel");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error saving to Excel: {ex.Message}");
                ShowToast("Lỗi", $"Không thể lưu vào Excel: {ex.Message}", ToastType.Error);
            }
        }

        private void StartMaintenanceWarningTimer()
        {
            _warningTimer = new DispatcherTimer();
            _warningTimer.Interval = TimeSpan.FromMinutes(1); // Check every 1 minute
            _warningTimer.Tick += (sender, e) => CheckMaintenanceWarnings();
            _warningTimer.Start();

            // Check immediately on startup
            CheckMaintenanceWarnings();
        }

        private void CheckMaintenanceWarnings()
        {
            try
            {
                var today = DateTime.Today;
                var overdueItems = MaintenanceItems.Where(item => item.NextMaintenanceDate < today).ToList();
                var dueSoonItems = MaintenanceItems.Where(item =>
                    item.NextMaintenanceDate >= today &&
                    item.NextMaintenanceDate <= today.AddDays(2)).ToList();

                if (overdueItems.Any())
                {
                    var overdueCount = overdueItems.Count;
                    ShowToast("Cảnh báo bảo dưỡng",
                        $"Có {overdueCount} mục bảo dưỡng đã quá hạn!",
                        ToastType.Error);

                    System.Diagnostics.Debug.WriteLine($"Maintenance Warning: {overdueCount} overdue items");
                }
                else if (dueSoonItems.Any())
                {
                    var dueSoonCount = dueSoonItems.Count;
                    ShowToast("Nhắc nhở bảo dưỡng",
                        $"Có {dueSoonCount} mục bảo dưỡng sắp đến hạn trong 2 ngày tới!",
                        ToastType.Warning);

                    System.Diagnostics.Debug.WriteLine($"Maintenance Warning: {dueSoonCount} due soon items");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Maintenance: Error checking warnings: {ex.Message}");
            }
        }



        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }


}
