﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    public class DataAggregatorService
    {
        // Key là SequenceNumber. Giả sử SequenceNumber là duy nhất và đến tương đối sớm.
        // Value là ProductRecord đang được xây dựng.
        private readonly ConcurrentDictionary<int, ProductRecord> _pendingRecords;
        private readonly object _lock = new object(); // Lock cho các thao tác phức tạp hơn nếu cần

        // Event để thông báo khi một bản ghi hoàn chỉnh
        public event EventHandler<ProductRecordEventArgs> RecordCompleted;

        public DataAggregatorService()
        {
            _pendingRecords = new ConcurrentDictionary<int, ProductRecord>();
        }

        // Phương thức nhận S<PERSON> thứ tự và Kết quả Test
        // Giả sử chúng đến cùng nhau từ một nguồn (ví dụ: PLC máy test)
        public void ReceiveSequenceAndTestResult(int sequenceNumber, TestResultStatus testResult, string testMachineId = "DefaultTestMachine")
        {
            if (testResult == TestResultStatus.None || testResult == TestResultStatus.Testing || testResult == TestResultStatus.AwaitingProduct)
            {
                Debug.WriteLine($"DataAggregator: Ignoring intermediate test result {testResult} for sequence {sequenceNumber}. Waiting for final OK/NG.");
                return; // Chỉ xử lý kết quả cuối cùng OK/NG
            }

            Debug.WriteLine($"DataAggregator: Received Seq={sequenceNumber}, Result={testResult}, Machine={testMachineId}");

            ProductRecord record = _pendingRecords.GetOrAdd(sequenceNumber, (seq) =>
            {
                Debug.WriteLine($"DataAggregator: Creating new pending record for Seq={sequenceNumber}");
                return new ProductRecord { SequenceNumber = seq };
            });

            record.SequenceNumber = sequenceNumber; // Đảm bảo SequenceNumber được set (có thể GetOrAdd không trigger setter)
            record.TestResult = testResult;
            record.TestMachineId = testMachineId;

            CheckAndProcessCompleteRecord(sequenceNumber, record);
        }

        // Phương thức nhận Mã sản phẩm
        // Giả sử mã sản phẩm có thể đến sau và được liên kết bằng SequenceNumber
        public void ReceiveProductCode(int sequenceNumber, string productCode)
        {
            Debug.WriteLine($"DataAggregator: Received Code={productCode} for Seq={sequenceNumber}");

            if (string.IsNullOrEmpty(productCode))
            {
                Debug.WriteLine($"DataAggregator: Ignoring empty product code for sequence {sequenceNumber}.");
                return;
            }

            ProductRecord record = _pendingRecords.GetOrAdd(sequenceNumber, (seq) =>
            {
                Debug.WriteLine($"DataAggregator: Creating new pending record for Seq={sequenceNumber} (from product code)");
                return new ProductRecord { SequenceNumber = seq };
            });

            // Nếu record đã có SequenceNumber (từ GetOrAdd) thì không cần set lại
            // Nhưng nếu record mới được tạo, SequenceNumber có thể chưa được set bởi setter nếu key được dùng trực tiếp.
            if (!record.SequenceNumber.HasValue) record.SequenceNumber = sequenceNumber;

            record.ProductCode = productCode;

            CheckAndProcessCompleteRecord(sequenceNumber, record);
        }

        private void CheckAndProcessCompleteRecord(int sequenceNumber, ProductRecord record)
        {
            if (record.IsComplete)
            {
                Debug.WriteLine($"DataAggregator: Record for Seq={sequenceNumber} is complete: {record}");
                OnRecordCompleted(record);

                // Xóa bản ghi khỏi bộ đệm sau khi xử lý
                if (_pendingRecords.TryRemove(sequenceNumber, out ProductRecord removedRecord))
                {
                    Debug.WriteLine($"DataAggregator: Removed completed record for Seq={sequenceNumber} from pending queue.");
                }
                else
                {
                    Debug.WriteLine($"DataAggregator: Warning - Failed to remove completed record for Seq={sequenceNumber} from pending queue. It might have been removed already.");
                }
            }
            else
            {
                Debug.WriteLine($"DataAggregator: Record for Seq={sequenceNumber} is still incomplete. Current state: {record}");
            }
        }

        protected virtual void OnRecordCompleted(ProductRecord record)
        {
            RecordCompleted?.Invoke(this, new ProductRecordEventArgs(record));
        }

        // Có thể thêm phương thức dọn dẹp các bản ghi quá hạn (timeout) nếu cần
        public void CleanupStaleRecords(TimeSpan maxAge)
        {
            var now = DateTime.Now;
            var staleKeys = _pendingRecords
                .Where(kvp => (now - kvp.Value.RecordTimestamp) > maxAge && !kvp.Value.IsComplete)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in staleKeys)
            {
                if (_pendingRecords.TryRemove(key, out ProductRecord removedRecord))
                {
                    Debug.WriteLine($"DataAggregator: Cleaned up stale record for Seq={key}. Record: {removedRecord}");
                    // Có thể log những bản ghi này vào một nơi riêng để điều tra
                }
            }
        }
    }

    // EventArgs cho event RecordCompleted
    public class ProductRecordEventArgs : EventArgs
    {
        public ProductRecord Record { get; }
        public ProductRecordEventArgs(ProductRecord record)
        {
            Record = record;
        }
    }
}
