﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public class ProductItem : INotifyPropertyChanged
    {
        private string _productId;
        public string ProductId
        {
            get => _productId;
            set { _productId = value; OnPropertyChanged(nameof(ProductId)); }
        }

        private string _productCode;
        public string ProductCode
        {
            get => _productCode;
            set { _productCode = value; OnPropertyChanged(nameof(ProductCode)); }
        }

        private TestResultStatus _currentTestResult;
        public TestResultStatus CurrentTestResult
        {
            get => _currentTestResult;
            set { _currentTestResult = value; OnPropertyChanged(nameof(CurrentTestResult)); }
        }

        // To track which station it's logically associated with.
        // Could be an index or a direct reference to StationControl if needed by ProductItem itself.
        // For now, an index into MainlineLayout's list of stations is simpler.
        private int _currentStationIndex = -1; // -1 means not on any station / off-line
        public int CurrentStationIndex
        {
            get => _currentStationIndex;
            set { _currentStationIndex = value; OnPropertyChanged(nameof(CurrentStationIndex)); }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public ProductItem(string productId, string productCode)
        {
            ProductId = productId;
            ProductCode = productCode;
            CurrentTestResult = TestResultStatus.AwaitingProduct; // Default
        }
    }
}
