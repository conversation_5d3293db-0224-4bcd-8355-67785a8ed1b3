<UserControl x:Class="ZoomableApp.Views.MaintenancePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:ZoomableApp.ViewModels"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <UserControl.DataContext>
        <viewmodels:MaintenanceViewModel />
    </UserControl.DataContext>
    
    <UserControl.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                               CornerRadius="5" 
                               BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Status Color Converter -->
        <Style x:Key="StatusRowStyle" TargetType="DataGridRow">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Overdue">
                    <Setter Property="Background" Value="#E74C3C"/>
                    <Setter Property="Foreground" Value="White"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="DueSoon">
                    <Setter Property="Background" Value="#F39C12"/>
                    <Setter Property="Foreground" Value="White"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Normal">
                    <Setter Property="Background" Value="#2ECC71"/>
                    <Setter Property="Foreground" Value="White"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="#2C3E50">
        <Grid.RowDefinitions>
            <RowDefinition Height="0.1*"/>  <!-- Header: 10% -->
            <RowDefinition Height="0.2*"/>  <!-- Filter & Results: 20% -->
            <RowDefinition Height="0.7*"/>  <!-- Main Table: 70% -->
        </Grid.RowDefinitions>
        
        <!-- Header Section (10%) -->
        <Grid Grid.Row="0" Background="#34495E" Margin="10,10,10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.3*"/>  <!-- Title + Tabs: 30% -->
                <ColumnDefinition Width="0.7*"/>  <!-- Date filters + Buttons: 70% -->
            </Grid.ColumnDefinitions>

            <!-- Left: Title + Navigation Tabs -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                <TextBlock Text="LỊCH BẢO DƯỠNG"
                          FontSize="18" FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"
                          Margin="0,0,20,0"/>

                <!-- Navigation Tabs -->
                <Button Content="Tổng hợp"
                       Background="#3498DB"
                       Foreground="White"
                       FontWeight="Bold"
                       Padding="12,6"
                       Margin="5,0"
                       BorderThickness="0"
                       Command="{Binding RefreshCommand}"
                       Style="{StaticResource ModernButtonStyle}"/>

                <Button Content="Trong tháng"
                       Background="#9B59B6"
                       Foreground="White"
                       FontWeight="Bold"
                       Padding="12,6"
                       Margin="5,0"
                       BorderThickness="0"
                       Style="{StaticResource ModernButtonStyle}"/>
            </StackPanel>

            <!-- Right: Date Filters + Buttons -->
            <StackPanel Grid.Column="1" Orientation="Horizontal"
                       HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,15,0">

                <!-- Date Range -->
                <DatePicker SelectedDate="{Binding FromDate}"
                           Margin="5,0"
                           VerticalAlignment="Center"
                           Width="120"/>

                <TextBlock Text="Đến"
                          Foreground="White"
                          VerticalAlignment="Center"
                          Margin="5,0"/>

                <DatePicker SelectedDate="{Binding ToDate}"
                           Margin="5,0"
                           VerticalAlignment="Center"
                           Width="120"/>

                <!-- History Button -->
                <Button Content="Lịch sử"
                       Background="#3498DB"
                       Foreground="White"
                       FontWeight="Bold"
                       Padding="12,6"
                       Margin="10,0,5,0"
                       BorderThickness="0"
                       Command="{Binding ViewHistoryCommand}"
                       Style="{StaticResource ModernButtonStyle}"/>

                <!-- Export Button -->
                <Button Content="Xuất Excel"
                       Background="#8E44AD"
                       Foreground="White"
                       FontWeight="Bold"
                       Padding="12,6"
                       Margin="5,0"
                       BorderThickness="0"
                       Command="{Binding ExportExcelCommand}"
                       Style="{StaticResource ModernButtonStyle}"/>
            </StackPanel>
        </Grid>
        
        <!-- Filter & Results Section (20%) -->
        <Grid Grid.Row="1" Background="#34495E" Margin="10,5,10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>  <!-- Left: Mục bảo dưỡng -->
                <ColumnDefinition Width="*"/>  <!-- Right: Kết quả bảo dưỡng -->
            </Grid.ColumnDefinitions>

            <!-- Left: Mục bảo dưỡng -->
            <Border Grid.Column="0" Background="#2C3E50" CornerRadius="5" Margin="0,0,5,0">
                <StackPanel Margin="15">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="MỤC BẢO DƯỠNG"
                                  FontSize="14" FontWeight="Bold"
                                  Foreground="White"
                                  Margin="0,0,20,0"/>
                        <TextBlock Text="Date:"
                                  FontSize="12"
                                  Foreground="#BDC3C7"
                                  Margin="0,0,5,0"/>
                        <TextBlock x:Name="TodayDateText"
                                  Text="{Binding Source={x:Static sys:DateTime.Today}, StringFormat='{}{0:dd/MM/yyyy}'}"
                                  FontSize="12"
                                  Foreground="White"/>
                    </StackPanel>

                    <!-- Excel File Info -->
                    <StackPanel Margin="0,0,0,10">
                        <TextBlock Text="{Binding ExcelFilePath}"
                                  FontSize="11"
                                  Foreground="#3498DB"
                                  Margin="0,0,0,2"/>
                        <TextBlock Text="{Binding ExcelFileStatus}"
                                  FontSize="10"
                                  Foreground="#BDC3C7"/>
                    </StackPanel>

                    <TextBlock x:Name="SelectedContentText"
                              Text="{Binding SelectedItem.Content}"
                              Foreground="White"
                              FontSize="12"
                              TextWrapping="Wrap"
                              Margin="0,5"/>

                    <TextBlock x:Name="SelectedMethodText"
                              Text="{Binding SelectedItem.Method}"
                              Foreground="#BDC3C7"
                              FontSize="11"
                              TextWrapping="Wrap"
                              Margin="0,5"/>
                </StackPanel>
            </Border>

            <!-- Right: Kết quả bảo dưỡng -->
            <Border Grid.Column="1" Background="#2C3E50" CornerRadius="5" Margin="5,0,0,0">
                <StackPanel Margin="15">
                    <TextBlock Text="KẾT QUẢ BẢO DƯỠNG"
                              FontSize="14" FontWeight="Bold"
                              Foreground="White"
                              Margin="0,0,0,10"/>

                    <TextBox x:Name="MaintenanceResultTextBox"
                            Text="{Binding MaintenanceResult}"
                            Height="60"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"
                            Margin="0,0,0,10"/>

                    <TextBlock Text="*Nhân viên bảo dưỡng xác nhận kết quả bảo dưỡng"
                              Foreground="#BDC3C7"
                              FontSize="10"
                              TextWrapping="Wrap"
                              Margin="0,0,0,10"/>

                    <Button Content="XÁC NHẬN"
                           Foreground="White"
                           FontWeight="Bold"
                           FontSize="14"
                           Padding="20,10"
                           BorderThickness="0"
                           Command="{Binding PerformMaintenanceCommand}"
                           HorizontalAlignment="Center"
                           Cursor="Hand"
                           ToolTip="Click để xác nhận bảo dưỡng. Vui lòng chọn mục bảo dưỡng và nhập kết quả trước khi xác nhận.">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
                                <Setter Property="Background" Value="#27AE60"/>
                                <Setter Property="Opacity" Value="1"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#2ECC71"/>
                                        <Setter Property="RenderTransform">
                                            <Setter.Value>
                                                <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#229954"/>
                                        <Setter Property="RenderTransform">
                                            <Setter.Value>
                                                <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Main Table Section (70%) -->
        <Border Grid.Row="2" Background="#34495E" CornerRadius="5" Margin="10,5,10,10">
            <DataGrid ItemsSource="{Binding FilteredItems}"
                     SelectedItem="{Binding SelectedItem}"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     Background="Transparent"
                     Foreground="Black"
                     RowStyle="{StaticResource StatusRowStyle}"
                     Margin="10"
                     FontSize="12">

                <DataGrid.Columns>
                    <DataGridTextColumn Header="NoID" Binding="{Binding Id}" Width="60"/>
                    <DataGridTextColumn Header="Content" Binding="{Binding Content}" Width="200"/>
                    <DataGridTextColumn Header="Method" Binding="{Binding Method}" Width="250"/>
                    <DataGridTextColumn Header="Cycle" Binding="{Binding Cycle}" Width="80"/>
                    <DataGridTextColumn Header="Guide" Binding="{Binding Guide}" Width="200"/>
                    <DataGridTextColumn Header="Last" Binding="{Binding LastMaintenanceDateText}" Width="100"/>
                    <DataGridTextColumn Header="Plans" Binding="{Binding NextMaintenanceDateText}" Width="100"/>
                    <DataGridTextColumn Header="MaintainResult" Binding="{Binding Result}" Width="120"/>
                    <DataGridTextColumn Header="MaintainedDate" Binding="{Binding LastMaintenanceDateText}" Width="100"/>
                    <DataGridTextColumn Header="MaintainedBy" Binding="{Binding PerformedBy}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</UserControl>
