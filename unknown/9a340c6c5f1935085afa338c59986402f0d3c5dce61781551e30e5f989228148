﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    public static class PlcDataMapper
    {
        // Chuyển đổi giá trị object từ PLC (thường là short, int, bool, byte[]) sang kiểu dữ liệu ứng dụng
        public static T MapToApplicationType<T>(object plcValue, PlcRegisterInfo regInfo)
        {
            if (plcValue == null) return default(T);

            Type targetType = typeof(T);

            try
            {
                if (targetType.IsEnum)
                {
                    // Nếu plcValue là short hoặc int, và T là Enum
                    if (plcValue is short shortVal) return (T)Enum.ToObject(targetType, (int)shortVal);
                    if (plcValue is int intVal) return (T)Enum.ToObject(targetType, intVal);
                    // <PERSON><PERSON> th<PERSON> cần xử lý các kiểu khác nếu PLC trả về kiểu khác cho enum
                }

                if (targetType == typeof(string) && regInfo.DataType == PlcDataType.STRING)
                {
                    // Giả sử plcValue là string (trong mô phỏng) hoặc byte[] (từ PLC thực)
                    if (plcValue is string strVal) return (T)(object)strVal.TrimEnd('\0'); // Loại bỏ ký tự null

                    if (plcValue is short[] shortArray) // PLC thường trả về mảng WORD (short) cho string
                    {
                        byte[] byteArray = new byte[shortArray.Length * 2];
                        Buffer.BlockCopy(shortArray, 0, byteArray, 0, byteArray.Length);
                        // Cần biết encoding của PLC, thường là ASCII hoặc Shift-JIS
                        return (T)(object)Encoding.ASCII.GetString(byteArray).TrimEnd('\0');
                    }
                    // Xử lý các trường hợp khác nếu cần
                }

                // Chuyển đổi trực tiếp nếu kiểu khớp hoặc có thể chuyển đổi ngầm định
                return (T)Convert.ChangeType(plcValue, targetType);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error mapping PLC value '{plcValue}' (Type: {plcValue.GetType()}) for {regInfo.LogicalName} to type {targetType}: {ex.Message}");
                return default(T);
            }
        }

        // Chuyển đổi giá trị từ ứng dụng sang kiểu dữ liệu mà PLC mong đợi
        public static object MapToPlcType(object appValue, PlcRegisterInfo regInfo)
        {
            if (appValue == null) return null; // Hoặc giá trị mặc định cho PLC

            try
            {
                if (regInfo.DataType == PlcDataType.BIT)
                {
                    return Convert.ToBoolean(appValue); // PLC thường nhận 0 hoặc 1 cho bit
                }
                if (regInfo.DataType == PlcDataType.WORD)
                {
                    if (appValue is Enum) return (short)Convert.ChangeType(appValue, Enum.GetUnderlyingType(appValue.GetType()));
                    return Convert.ToInt16(appValue);
                }
                if (regInfo.DataType == PlcDataType.DWORD)
                {
                    if (appValue is Enum) return (int)Convert.ChangeType(appValue, Enum.GetUnderlyingType(appValue.GetType()));
                    return Convert.ToInt32(appValue);
                }
                if (regInfo.DataType == PlcDataType.STRING && appValue is string strAppValue)
                {
                    // Chuyển string thành mảng byte/short theo encoding của PLC
                    // Giả sử ASCII và PLC nhận mảng WORD (short)
                    byte[] bytes = Encoding.ASCII.GetBytes(strAppValue);
                    // Đảm bảo độ dài không vượt quá số WORD đã cấp phát (regInfo.Length)
                    int maxBytes = regInfo.Length * 2;
                    if (bytes.Length > maxBytes)
                    {
                        Array.Resize(ref bytes, maxBytes);
                    }
                    else if (bytes.Length < maxBytes)
                    {
                        // Pad với null terminator nếu cần, hoặc PLC tự xử lý
                        var paddedBytes = new byte[maxBytes];
                        Array.Copy(bytes, paddedBytes, bytes.Length);
                        bytes = paddedBytes; // Đảm bảo đủ số byte cho số WORD
                    }

                    short[] shortArray = new short[regInfo.Length];
                    if (bytes.Length > 0)
                    {
                        // Nếu số byte lẻ, word cuối cùng sẽ bị mất 1 byte khi copy BlockCopy nếu không cẩn thận
                        // Buffer.BlockCopy(bytes, 0, shortArray, 0, bytes.Length);
                        // Cách an toàn hơn:
                        for (int i = 0; i < regInfo.Length; i++)
                        {
                            if (i * 2 + 1 < bytes.Length) // Có đủ 2 byte
                            {
                                shortArray[i] = BitConverter.ToInt16(bytes, i * 2);
                            }
                            else if (i * 2 < bytes.Length) // Chỉ có 1 byte cuối
                            {
                                byte[] temp = new byte[2];
                                temp[0] = bytes[i * 2];
                                temp[1] = 0; // Pad với null
                                shortArray[i] = BitConverter.ToInt16(temp, 0);
                            }
                            else
                            {
                                shortArray[i] = 0; // Pad với ký tự null (0x0000)
                            }
                        }
                    }
                    return shortArray;
                }
                // Các chuyển đổi khác
                return appValue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error mapping app value '{appValue}' for {regInfo.LogicalName} to PLC type: {ex.Message}");
                return null;
            }
        }
    }
}
