﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices; // For CallerMemberName
using System.Windows.Threading;      // For DispatcherTimer
using ZoomableApp.Models;
using ZoomableApp.Layouts;
using ZoomableApp.SharedControls;
using ZoomableApp.Services;
using ZoomableApp.PLC;
using ZoomableApp.ViewModels;
using System.Diagnostics;
using System.Windows.Media.Animation;



namespace ZoomableApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private Point _panStartPoint;
        private Point _panStartTranslate;
        private bool _isPanning;
        private ToastNotificationPanel _toastNotification;
        private readonly PlcConnectionManager _plcManager;
        private readonly DataAggregatorService _dataAggregator;
        private readonly IDatabaseService _databaseService;
        private DispatcherTimer _plcDataReaderTimer; // Timer để đọc dữ liệu từ PLC định kỳ
        private HomeDailyPlanViewModel _dailyPlanViewModel;

        public ObservableCollection<PlcConnectionUIData> PlcConnections { get; set; }

        public ICommand ConnectPlcCommand { get; }
        public ICommand DisconnectPlcCommand { get; }
        public ICommand WriteToPlcCommand { get; }
        public ICommand ReadFromPlcCommand { get; }

        private const double ZoomSpeed = 1.1;
        private const double MaxZoom = 5.0;
        private const double MinZoom = 0.2;

        // Instances of the layouts to manage event subscriptions and product transfer
        private MainlineLayout _mainlineLayoutInstance;
        private InspectionLayout _inspectionLayoutInstance;
        // A general reference to the currently loaded UserControl layout
        private UserControl _currentLoadedLayout;

        private ObservableCollection<ErrorNotification> _allErrors = new ObservableCollection<ErrorNotification>();
        public ObservableCollection<ErrorNotification> AllErrors
        {
            get => _allErrors;
            set { _allErrors = value; OnPropertyChanged(); }
        }
        private int _newErrorCount;
        public int NewErrorCount
        {
            get => _newErrorCount;
            set { _newErrorCount = value; OnPropertyChanged(); OnPropertyChanged(nameof(HasNewErrors)); }
        }

        public bool HasNewErrors => NewErrorCount > 0;

        private bool _isErrorListPopupOpen;
        public bool IsErrorListPopupOpen
        {
            get => _isErrorListPopupOpen;
            set { _isErrorListPopupOpen = value; OnPropertyChanged(); }
        }

        private string _valueToWrite = "123"; // Giá trị mặc định để ghi
        public string ValueToWrite
        {
            get => _valueToWrite;
            set { _valueToWrite = value; OnPropertyChanged(); }
        }

        private string _readValue = ""; // Giá trị đọc được (hiển thị)
        public string ReadValue
        {
            get => _readValue;
            set { _readValue = value; OnPropertyChanged(); }
        }
        private bool _isSidebarOpen = false;
        public bool IsSidebarOpen
        {
            get => _isSidebarOpen;
            set { _isSidebarOpen = value; OnPropertyChanged(); }
        }

        // private DispatcherTimer _plcErrorSimulatorTimer; //phục vụ mô phỏng lỗi
        //private int _simulatedErrorCounter = 0; // phục vụ mô phỏng lỗi
        //private Random _random = new Random(); // phục vụ mô phỏng lỗi



        public MainWindow()
        {
            InitializeComponent();
            DataContext = this; // Set DataContext for bindings to MainWindow properties

            var loadedPlcConfigs = ConfigLoader.LoadPlcConfigs();



            _plcManager = new PlcConnectionManager(loadedPlcConfigs);
            PlcConnections = new ObservableCollection<PlcConnectionUIData>();
            // PlcConnectionsItemsControl đã bị xóa, không cần gán ItemsSource nữa

            //Đăng kí sự kiện cho plc manager
            _plcManager.PlcConnectionFailed += PlcManager_PlcConnectionFailed;

            //Cập nhật UI (PlcConnections là ObservableCollection<PlcConnectionUIData>)
            PlcConnections = new ObservableCollection<PlcConnectionUIData>();
            foreach (var configInfo in _plcManager.GetAllPlcConnectionInfos()) // Lấy từ manager
            {
                PlcConnections.Add(new PlcConnectionUIData(configInfo));
            }
            // PlcConnectionsItemsControl đã bị xóa, không cần gán ItemsSource nữa

            LoadLayout("Mainline");
            LayoutSelectorComboBox.SelectedIndex = 0;

            // Cập nhật thông tin user trong sidebar
            UpdateUserInfo();

            ConnectPlcCommand = new RelayCommand<string>(async (plcId) => await ExecuteConnectPlc(plcId), CanExecuteConnectPlc);
            DisconnectPlcCommand = new RelayCommand<string>(async (plcId) => await ExecuteDisconnectPlc(plcId), CanExecuteDisconnectPlc);
            ConnectPlcCommand = new RelayCommand<string>(async (plcId) => await ExecuteConnectPlc(plcId), CanExecuteConnectPlc);
            DisconnectPlcCommand = new RelayCommand<string>(async (plcId) => await ExecuteDisconnectPlc(plcId), CanExecuteDisconnectPlc);
            WriteToPlcCommand = new RelayCommand<PlcConnectionUIData>(async (plcUiData) => await ExecuteWriteToPlc(plcUiData), CanExecuteReadWritePlc);
            ReadFromPlcCommand = new RelayCommand<PlcConnectionUIData>(async (plcUiData) => await ExecuteReadFromPlc(plcUiData), CanExecuteReadWritePlc);

            _ = AutoConnectPlcsOnStartup(); // Chạy và không chờ (fire and forget) - UI sẽ cập nhật sau

            _dataAggregator = new DataAggregatorService();
            _databaseService = new SimulatedDatabaseService(); // inject qua DI
            _dataAggregator.RecordCompleted += DataAggregator_RecordCompleted;

            // Initialize Error Simulation
            InitializeToastNotification();
            //InitializeErrorSimulation(); // Mô phỏng lỗi

            // Khởi tạo timer để đọc dữ liệu từ PLC
            InitializePlcDataReaderTimer();
            UpdatePlcDataReaderTimerState();

            SidebarPanel.Visibility = Visibility.Collapsed;
            SidebarColumn.Width = new GridLength(0, GridUnitType.Pixel); // Thu gọn hoàn toàn

            // Initialize Daily Plan ViewModel
            _dailyPlanViewModel = new HomeDailyPlanViewModel();
            DailyPlanSection.DataContext = _dailyPlanViewModel;

            this.Closing += MainWindow_Closing;
        }

        // Debug event handlers for Daily Plan buttons
        private void RefreshPlanButton_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("RefreshPlanButton clicked!");
        }

        private void MarkCompleteButton_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("MarkCompleteButton clicked!");
        }

        private void StartNextButton_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("StartNextButton clicked!");
        }

        private void OpenSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            IsSidebarOpen = true;
            AnimateSidebar(true);
        }

        private void CloseSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            IsSidebarOpen = false;
            AnimateSidebar(false);
        }

        private void SidebarMenuListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SidebarMenuListBox.SelectedItem is ListBoxItem selectedItem)
            {
                string menuText = selectedItem.Content.ToString();
                System.Diagnostics.Debug.WriteLine($"Menu selected: '{menuText}'");
                SwitchToPage(menuText);

                // Đóng sidebar sau khi chọn menu (tùy chọn)
                IsSidebarOpen = false;
                AnimateSidebar(false);
            }
        }

        private void SwitchToPage(string pageName)
        {
            System.Diagnostics.Debug.WriteLine($"SwitchToPage called with: '{pageName}'");

            // Ẩn tất cả các trang
            ZoomPanPage.Visibility = Visibility.Collapsed;
            PlanPage.Visibility = Visibility.Collapsed;
            ReportPage.Visibility = Visibility.Collapsed;
            MaintenancePage.Visibility = Visibility.Collapsed;
            AccountPage.Visibility = Visibility.Collapsed;

            // Xử lý text menu (loại bỏ emoji và khoảng trắng thừa)
            string cleanPageName = pageName?.Trim();
            if (cleanPageName != null)
            {
                // Loại bỏ emoji ở đầu bằng cách tìm khoảng trắng đầu tiên
                int spaceIndex = cleanPageName.IndexOf(' ');
                if (spaceIndex > 0)
                {
                    cleanPageName = cleanPageName.Substring(spaceIndex + 1).Trim();
                }
            }

            System.Diagnostics.Debug.WriteLine($"Clean page name: '{cleanPageName}'");

            // Hiển thị trang được chọn và cập nhật tiêu đề
            switch (cleanPageName)
            {
                case "Trang chủ":
                    ZoomPanPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Trang chủ");
                    break;
                case "Kế hoạch":
                    PlanPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Kế hoạch");
                    break;
                case "Báo cáo":
                    ReportPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Báo cáo");
                    break;
                case "Bảo dưỡng":
                    MaintenancePage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Bảo dưỡng");
                    break;
                case "Tài khoản":
                    AccountPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Tài khoản");
                    break;
                default:
                    ZoomPanPage.Visibility = Visibility.Visible; // Mặc định về trang chủ
                    UpdatePageTitle("Trang chủ");
                    System.Diagnostics.Debug.WriteLine($"Unknown page name: '{cleanPageName}', defaulting to Trang chủ");
                    break;
            }
        }

        private void UpdatePageTitle(string title)
        {
            if (PageTitleTextBlock != null)
            {
                PageTitleTextBlock.Text = title;
                System.Diagnostics.Debug.WriteLine($"Page title updated to: '{title}'");
            }
        }

        private void AnimateSidebar(bool open)
        {
            // Kích thước mong muốn của sidebar
            double targetWidth = open ? 200 : 0; // 200px khi mở, 0px khi đóng
            Duration duration = new Duration(TimeSpan.FromSeconds(0.3));

            if (open)
            {
                SidebarPanel.Visibility = Visibility.Visible;
                // Set width ngay lập tức để có thể animate
                SidebarColumn.Width = new GridLength(targetWidth, GridUnitType.Pixel);

                // Animate opacity từ 0 đến 1
                var opacityAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = duration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                SidebarPanel.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
            else
            {
                // Animate opacity từ 1 đến 0
                var opacityAnimation = new DoubleAnimation
                {
                    From = 1,
                    To = 0,
                    Duration = duration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
                };

                opacityAnimation.Completed += (s, e) =>
                {
                    SidebarPanel.Visibility = Visibility.Collapsed;
                    SidebarColumn.Width = new GridLength(0, GridUnitType.Pixel);
                };

                SidebarPanel.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private bool CanExecuteReadWritePlc(PlcConnectionUIData plcUiData)
        {
            return plcUiData != null && plcUiData.IsConnected;
        }

        private async Task ExecuteWriteToPlc(PlcConnectionUIData plcUiData)
        {
            if (plcUiData == null || !plcUiData.IsConnected)
            {
                AddNewError(plcUiData?.PlcId ?? "UnknownPLC", "Cannot write: PLC not connected.");
                return;
            }

            var plcService = _plcManager.GetPlcService(plcUiData.PlcId);
            if (plcService == null)
            {
                AddNewError(plcUiData.PlcId, "PLC service not found for writing.");
                return;
            }

            // Sử dụng PlcDeviceAddress đã định nghĩa cho thanh ghi test
            PlcDeviceAddress targetDeviceAddress = PlcDeviceAddress.BitTest;
            var regInfo = plcService.GetRegisterInfo(targetDeviceAddress); // Lấy thông tin thanh ghi (bao gồm DataType)

            object valueToActuallyWrite = null;
            try
            {
                if (regInfo.DataType == PlcDataType.WORD)
                {
                    valueToActuallyWrite = short.Parse(plcUiData.ValueToWrite);
                }
                else if (regInfo.DataType == PlcDataType.BIT)
                {
                    bool successfullyParsedBit = false;
                    if (bool.TryParse(plcUiData.ValueToWrite, out bool bVal))
                    {
                        valueToActuallyWrite = bVal;
                        successfullyParsedBit = true;
                        System.Diagnostics.Debug.WriteLine($"Parsed BIT value: {bVal}"); // Debug log
                    }
                    else if (int.TryParse(plcUiData.ValueToWrite, out int iValForBit)) // Đổi tên biến để rõ ràng
                    {
                        if (iValForBit == 0 || iValForBit == 1)
                        {
                            valueToActuallyWrite = (iValForBit != 0);
                            successfullyParsedBit = true;
                        }
                    }

                    if (!successfullyParsedBit)
                    {
                        throw new FormatException("Invalid value for BIT. Expected 'true', 'false', '0', or '1'.");
                    }
                }
                // Thêm các kiểu khác nếu TestData có thể thay đổi kiểu
                else if (regInfo.DataType == PlcDataType.DWORD)
                {
                    valueToActuallyWrite = int.Parse(plcUiData.ValueToWrite);
                }
                else if (regInfo.DataType == PlcDataType.FLOAT)
                {
                    valueToActuallyWrite = float.Parse(plcUiData.ValueToWrite);
                }
                else
                {
                    AddNewError(plcUiData.PlcId, $"Writing to {targetDeviceAddress} with DataType {regInfo.DataType} is not fully handled in this example. Value: {plcUiData.ValueToWrite}");
                    return;
                }
            }
            catch (Exception ex)
            {
                AddNewError(plcUiData.PlcId, $"Invalid value format '{plcUiData.ValueToWrite}' for {regInfo.DataType}. Error: {ex.Message}");
                return;
            }

            PlcWriteResult result = await plcService.WriteAsync(targetDeviceAddress, valueToActuallyWrite);

            if (result.IsSuccess)
            {
                plcUiData.ReadValue = $"Write D0 OK: {valueToActuallyWrite}";
            }
            else
            {
                AddNewError(plcUiData.PlcId, $"Failed to write to D0 ({targetDeviceAddress}) on {plcUiData.Name}. Error: {result.Message}");
                plcUiData.ReadValue = $"Write D0 Fail: {result.Message}";
            }
            CommandManager.InvalidateRequerySuggested();
        }

        private async Task ExecuteReadFromPlc(PlcConnectionUIData plcUiData)
        {
            if (plcUiData == null || !plcUiData.IsConnected)
            {
                AddNewError(plcUiData?.PlcId ?? "UnknownPLC", "Cannot read: PLC not connected.");
                return;
            }

            var plcService = _plcManager.GetPlcService(plcUiData.PlcId);
            if (plcService == null)
            {
                AddNewError(plcUiData.PlcId, "PLC service not found for reading.");
                return;
            }

            PlcDeviceAddress targetDeviceAddress = PlcDeviceAddress.BitTest;
            PlcReadResult result = await plcService.ReadAsync(targetDeviceAddress);

            if (result.IsSuccess)
            {
                var regInfo = plcService.GetRegisterInfo(targetDeviceAddress);
                string displayValue;
                if (result.Value != null)
                {
                    // Map sang kiểu cụ thể nếu cần, hoặc dùng ToString()
                    if (regInfo.DataType == PlcDataType.WORD && result.Value is short sVal) displayValue = sVal.ToString();
                    else if (regInfo.DataType == PlcDataType.BIT && result.Value is bool bVal) displayValue = bVal.ToString();
                    else if (regInfo.DataType == PlcDataType.DWORD && result.Value is int iVal) displayValue = iVal.ToString();
                    else if (regInfo.DataType == PlcDataType.FLOAT && result.Value is float fVal) displayValue = fVal.ToString("F2");
                    else if (regInfo.DataType == PlcDataType.STRING && result.Value is string strVal) displayValue = strVal.TrimEnd('\0'); // Loại bỏ ký tự null
                    else displayValue = result.Value.ToString(); // Mặc định
                }
                else
                {
                    displayValue = "NULL";
                }
                plcUiData.ReadValue = displayValue;
            }
            else
            {
                AddNewError(plcUiData.PlcId, $"Failed to read from D0 ({targetDeviceAddress}) on {plcUiData.Name}. Error: {result.Message}");
                plcUiData.ReadValue = $"Read D0 Fail: {result.Message}";
            }
            CommandManager.InvalidateRequerySuggested();
        }

        private void PlcManager_PlcConnectionFailed(object sender, PlcErrorEventArgs e)
        {
            // Đảm bảo chạy trên UI thread nếu event được kích hoạt từ thread khác
            Dispatcher.Invoke(() =>
            {
                AddNewError(e.PlcId, e.ErrorMessage); // Gọi AddNewError để hiển thị toast và notification
            });
        }

        private void DataAggregator_RecordCompleted(object sender, ProductRecordEventArgs e)
        {
            Debug.WriteLine($"MainWindow: Received completed record from Aggregator: {e.Record}");
            // Gọi service để lưu vào DB
            _databaseService.SaveProductRecordAsync(e.Record); // Có thể await nếu cần xử lý kết quả
        }

        private async Task AutoConnectPlcsOnStartup()
        {
            if (_plcManager != null)
            {
                await _plcManager.ConnectAllAutoConnectPlcsAsync();
                // Sau khi kết nối xong, cập nhật lại UI
                RefreshPlcConnectionStatusUI();
                UpdatePlcDataReaderTimerState(); // Cập nhật trạng thái timer sau khi auto-connect
            }
        }

        // Phương thức mới để làm mới trạng thái UI từ manager
        private void RefreshPlcConnectionStatusUI()
        {
            if (_plcManager == null || PlcConnections == null) return;

            foreach (var uiData in PlcConnections)
            {
                var serviceInfo = _plcManager.GetPlcConnectionInfo(uiData.PlcId);
                if (serviceInfo != null)
                {
                    uiData.UpdateFromServiceInfo(serviceInfo);
                }
            }
        }
        private async Task ExecuteConnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return;
            var uiData = PlcConnections.FirstOrDefault(p => p.PlcId == plcId);
            if (uiData == null) return;

            // Lấy config từ manager, không cần cập nhật từ UI nữa vì đã load từ file
            var configToConnect = _plcManager.GetPlcConnectionInfo(plcId);
            if (configToConnect == null)
            {
                uiData.ConnectionStatus = "Error: Config not found.";
                return;
            }

            uiData.ConnectionStatus = "Connecting...";
            bool success = await _plcManager.ConnectPlcAsync(plcId); // Manager sẽ dùng config đã load

            var updatedInfo = _plcManager.GetPlcConnectionInfo(plcId);
            if (updatedInfo != null) uiData.UpdateFromServiceInfo(updatedInfo);
            UpdatePlcDataReaderTimerState();
            CommandManager.InvalidateRequerySuggested();
        }
        private bool CanExecuteConnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return false;
            var plcInfo = _plcManager?.GetPlcConnectionInfo(plcId);
            return plcInfo != null && !plcInfo.IsConnected;
        }


        private async Task ExecuteDisconnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return;
            var uiData = PlcConnections.FirstOrDefault(p => p.PlcId == plcId);
            if (uiData != null) uiData.ConnectionStatus = "Disconnecting...";

            await _plcManager.DisconnectPlcAsync(plcId);

            var updatedInfo = _plcManager.GetPlcConnectionInfo(plcId);
            if (updatedInfo != null && uiData != null) uiData.UpdateFromServiceInfo(updatedInfo);
            UpdatePlcDataReaderTimerState();
        }
        private bool CanExecuteDisconnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return false;
            var plcInfo = _plcManager?.GetPlcConnectionInfo(plcId);
            return plcInfo != null && plcInfo.IsConnected;
        }

        private void InitializePlcDataReaderTimer()
        {
            _plcDataReaderTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // Đọc PLC mỗi 2 giây (điều chỉnh cho phù hợp)
            };
            _plcDataReaderTimer.Tick += PlcDataReaderTimer_Tick;
            // _plcDataReaderTimer.Start(); // Sẽ start sau khi PLC kết nối thành công
        }

        private void UpdatePlcDataReaderTimerState()
        {
            if (_plcDataReaderTimer == null || _plcManager == null)
            {
                // Chưa sẵn sàng để cập nhật trạng thái timer
                return;
            }

            // Kiểm tra xem có PLC nào đang thực sự kết nối không
            // bằng cách duyệt qua thông tin kết nối trong PlcConnectionManager
            bool anyPlcConnected = _plcManager.GetAllPlcConnectionInfos().Any(info => info.IsConnected);

            if (anyPlcConnected)
            {
                if (!_plcDataReaderTimer.IsEnabled) // Nếu có PLC kết nối và timer chưa chạy
                {
                    _plcDataReaderTimer.Start();
                    Debug.WriteLine("PLC Data Reader Timer STARTED because at least one PLC is connected.");
                }
            }
            else // Không có PLC nào đang kết nối
            {
                if (_plcDataReaderTimer.IsEnabled) // Nếu không có PLC kết nối và timer đang chạy
                {
                    _plcDataReaderTimer.Stop();
                    Debug.WriteLine("PLC Data Reader Timer STOPPED because no PLCs are connected.");
                }
            }
        }

        private async void PlcDataReaderTimer_Tick(object sender, EventArgs e)
        {
            if (_plcManager == null) return;

            // Lấy danh sách tất cả các PLC ID đã được cấu hình và đang có service (đã thử kết nối)
            var allConfiguredPlcIds = _plcManager.GetAllPlcConnectionInfos().Select(info => info.Id).ToList();

            foreach (var plcId in allConfiguredPlcIds)
            {
                var plcService = _plcManager.GetPlcService(plcId);
                var plcConfigInfo = _plcManager.GetPlcConnectionInfo(plcId); // Lấy thêm config info để biết Name

                if (plcService == null || !plcService.IsConnected || plcConfigInfo == null)
                {
                    // Bỏ qua nếu PLC không có service, không kết nối, hoặc không có config info
                    continue;
                }

                Debug.WriteLine($"PlcDataReaderTimer_Tick: Processing PLC ID '{plcId}' ({plcConfigInfo.Name})");

                

                // VÍ DỤ 1: Nếu PLC này là PLC đọc mã (dựa vào ID hoặc Name từ config)
                if (plcConfigInfo.Id.Equals("PLC1_Reader", StringComparison.OrdinalIgnoreCase) ||
                    plcConfigInfo.Name.Contains("Đọc Mã", StringComparison.OrdinalIgnoreCase))
                {
                    PlcReadResult productCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.ProductCode_ReaderStation_StartWord);
                    if (!productCodeReadResult.IsSuccess)
                    {
                        AddNewError($"{plcId}_ReadFail_Code", $"{plcConfigInfo.Name}: Read Product Code Error. Details: {productCodeReadResult.Message}");
                    }
                    else
                    {
                        string productCode = PlcDataMapper.MapToApplicationType<string>(productCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.ProductCode_ReaderStation_StartWord));
                        if (!string.IsNullOrWhiteSpace(productCode))
                        {
                            PlcReadResult sequenceForCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.CurrentProductSequenceNumberWord); // Giả sử địa chỉ này cũng trên PLC đọc mã
                            if (!sequenceForCodeReadResult.IsSuccess)
                            {
                                AddNewError($"{plcId}_ReadFail_SeqForCode", $"{plcConfigInfo.Name}: Read Sequence for Code Error. Details: {sequenceForCodeReadResult.Message}");
                            }
                            else
                            {
                                short seqForCodeShort = PlcDataMapper.MapToApplicationType<short>(sequenceForCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.CurrentProductSequenceNumberWord));
                                if (seqForCodeShort > 0)
                                {
                                    _dataAggregator.ReceiveProductCode(seqForCodeShort, productCode.Trim());
                                }
                            }
                        }
                    }
                }

                if (plcConfigInfo.Id.Equals("PLC1_Reader", StringComparison.OrdinalIgnoreCase) ||
                    plcConfigInfo.Name.Contains("Đọc Mã", StringComparison.OrdinalIgnoreCase))
                {
                    PlcReadResult productCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.ProductCode_ReaderStation_StartWord);
                    if (!productCodeReadResult.IsSuccess)
                    {
                        AddNewError($"{plcId}_ReadFail_Code", $"{plcConfigInfo.Name}: Read Product Code Error. Details: {productCodeReadResult.Message}");
                    }
                    else
                    {
                        string productCode = PlcDataMapper.MapToApplicationType<string>(productCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.ProductCode_ReaderStation_StartWord));
                        if (!string.IsNullOrWhiteSpace(productCode))
                        {
                            PlcReadResult sequenceForCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.CurrentProductSequenceNumberWord); // Giả sử địa chỉ này cũng trên PLC đọc mã
                            if (!sequenceForCodeReadResult.IsSuccess)
                            {
                                AddNewError($"{plcId}_ReadFail_SeqForCode", $"{plcConfigInfo.Name}: Read Sequence for Code Error. Details: {sequenceForCodeReadResult.Message}");
                            }
                            else
                            {
                                short seqForCodeShort = PlcDataMapper.MapToApplicationType<short>(sequenceForCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.CurrentProductSequenceNumberWord));
                                if (seqForCodeShort > 0)
                                {
                                    _dataAggregator.ReceiveProductCode(seqForCodeShort, productCode.Trim());
                                }
                            }
                        }
                    }
                }

                else if (plcConfigInfo.Id.Equals("PLC8_Tester", StringComparison.OrdinalIgnoreCase) ||
                         plcConfigInfo.Name.Contains("Máy Test", StringComparison.OrdinalIgnoreCase))
                {
                    PlcReadResult seqNumReadResult = await plcService.ReadAsync(PlcDeviceAddress.TestStation09_ProductSequenceNumberWord); // Địa chỉ ví dụ
                    PlcReadResult testResultReadResult = await plcService.ReadAsync(PlcDeviceAddress.TestStation09_ResultWord); // Địa chỉ ví dụ

                    if (!seqNumReadResult.IsSuccess)
                    {
                        AddNewError($"{plcId}_ReadFail_TestSeq", $"{plcConfigInfo.Name}: Read Test Sequence Error. Details: {seqNumReadResult.Message}");
                    }
                    if (!testResultReadResult.IsSuccess)
                    {
                        AddNewError($"{plcId}_ReadFail_TestResult", $"{plcConfigInfo.Name}: Read Test Result Error. Details: {testResultReadResult.Message}");
                    }

                    if (seqNumReadResult.IsSuccess && testResultReadResult.IsSuccess)
                    {
                        short seqNumShort = PlcDataMapper.MapToApplicationType<short>(seqNumReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.TestStation09_ProductSequenceNumberWord));
                        // Giả sử kết quả test là một WORD (short) mà TestResultStatus có thể được ép kiểu từ đó
                        short testResultShort = PlcDataMapper.MapToApplicationType<short>(testResultReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.TestStation09_ResultWord));
                        TestResultStatus testResult = (TestResultStatus)testResultShort; // Cẩn thận với ép kiểu trực tiếp này

                        if (seqNumShort > 0 && (testResult == TestResultStatus.OK || testResult == TestResultStatus.NG))
                        {
                            _dataAggregator.ReceiveSequenceAndTestResult(seqNumShort, testResult, $"{plcConfigInfo.Name}_TestStation09"); // Sử dụng tên PLC cho testMachineId
                        }
                    }
                }
            }
            _dataAggregator.CleanupStaleRecords(TimeSpan.FromMinutes(5));
        }
        private async void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            // Ngắt kết nối tất cả PLC khi đóng ứng dụng
            Debug.WriteLine("MainWindow closing. Disconnecting all PLCs...");
            if (_plcManager != null)
            {
                await _plcManager.DisconnectAllAsync();
            }
            Debug.WriteLine("All PLCs disconnect command sent.");
        }

        private void InitializeToastNotification()
        {
            _toastNotification = ToastNotificationPanel.Instance;
            _toastNotification.SetContainer(ToastNotificationContainer);
            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.Duration = 3000;
            _toastNotification.AnimationDuration = 300;
            _toastNotification.CornerRadius = 10;
            _toastNotification.EnableShadow = true;
        }

        // 3 hàm bên dưới phục vụ mô phỏng lỗi 

        //private void InitializeErrorSimulation()
        //{
        //    _plcErrorSimulatorTimer = new DispatcherTimer
        //    {
        //        Interval = TimeSpan.FromSeconds(_random.Next(5, 15)) // Random interval for new errors
        //    };
        //    _plcErrorSimulatorTimer.Tick += PlcErrorSimulatorTimer_Tick;
        //    _plcErrorSimulatorTimer.Start();
        //}

        //private void PlcErrorSimulatorTimer_Tick(object sender, EventArgs e)
        //{
        //    _simulatedErrorCounter++;
        //    string errorCode = $"PLC_ERR_{_simulatedErrorCounter:D3}";
        //    string message = GetRandomErrorMessage();
        //    AddNewError(errorCode, message);

        //    // Reschedule with a new random interval
        //    _plcErrorSimulatorTimer.Interval = TimeSpan.FromSeconds(_random.Next(8, 20));
        //}

        //private string GetRandomErrorMessage()
        //{
        //    string[] messages = {
        //        "Sensor ST05_ProductPresence failed.", "Emergency Stop activated on Line 1.",
        //        "Conveyor motor overload at Mainline.Station03.", "Test Station 09: Communication Timeout.",
        //        "Lifter mechanism jammed.", "Safety door SFT02 open.", "PLC Battery Low."
        //    };
        //    return messages[_random.Next(messages.Length)];
        //}

        public void AddNewError(string errorId, string message)
        {
            // Ensure execution on UI thread if called from elsewhere
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(() => AddNewError(errorId, message));
                return;
            }

            var newError = new ErrorNotification(errorId, message);
            AllErrors.Insert(0, newError); // Add to top of the list
            NewErrorCount++;

            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.ShowError(message);
            System.Diagnostics.Debug.WriteLine($"New Error: {errorId} - {message}");
        }

        private void ErrorBellButton_Click(object sender, RoutedEventArgs e)
        {
            IsErrorListPopupOpen = !IsErrorListPopupOpen;
            if (IsErrorListPopupOpen)
            {
                // Mark viewed errors as not new, but only if they were new
                foreach (var err in AllErrors.Where(er => er.IsNew).ToList()) // ToList to avoid modification issues
                {
                    err.IsNew = false;
                }
                NewErrorCount = 0; // Reset badge count
                // The BellShakeStoryboard will stop via DataTrigger ExitActions
            }
        }
        private void ClearAllErrorsButton_Click(object sender, RoutedEventArgs e)
        {
            // This only clears the display. PLC errors would need PLC acknowledgement.
            AllErrors.Clear();
            NewErrorCount = 0;
            IsErrorListPopupOpen = false; // Optionally close popup
        }

        // --- INotifyPropertyChanged Implementation ---
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void ViewportBorder_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            Point mousePosition = e.GetPosition(ZoomPanCanvas); // Zoom relative to mouse position within the canvas
            double currentScale = ViewScaleTransform.ScaleX;
            double newScale;

            if (e.Delta > 0) // Zoom in
            {
                newScale = currentScale * ZoomSpeed;
            }
            else // Zoom out
            {
                newScale = currentScale / ZoomSpeed;
            }

            newScale = Math.Max(MinZoom, Math.Min(newScale, MaxZoom)); // Clamp zoom level

            if (Math.Abs(newScale - currentScale) > 0.001) // Check if scale actually changed
            {
                // Calculate the translation adjustment needed to keep the mouse point fixed
                double oldX = ViewTranslateTransform.X;
                double oldY = ViewTranslateTransform.Y;

                ViewScaleTransform.ScaleX = newScale;
                ViewScaleTransform.ScaleY = newScale;

                // Adjust translation so the point under the mouse remains the same
                ViewTranslateTransform.X = mousePosition.X - (mousePosition.X - oldX) * (newScale / currentScale);
                ViewTranslateTransform.Y = mousePosition.Y - (mousePosition.Y - oldY) * (newScale / currentScale);
            }
        }

        private void ViewportBorder_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Allow panning with Middle or Right mouse button
            if (e.MiddleButton == MouseButtonState.Pressed || e.RightButton == MouseButtonState.Pressed)
            {
                _isPanning = true;
                _panStartPoint = e.GetPosition(ViewportBorder); // Pan relative to ViewportBorder
                _panStartTranslate = new Point(ViewTranslateTransform.X, ViewTranslateTransform.Y);
                ViewportBorder.Cursor = Cursors.ScrollAll;
                ViewportBorder.CaptureMouse(); // Capture mouse to ensure MouseMove/Up events are received
            }
        }

        private void ViewportBorder_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isPanning)
            {
                Point currentMousePosition = e.GetPosition(ViewportBorder);
                Vector delta = currentMousePosition - _panStartPoint;
                ViewTranslateTransform.X = _panStartTranslate.X + delta.X;
                ViewTranslateTransform.Y = _panStartTranslate.Y + delta.Y;
            }
        }

        private void ViewportBorder_MouseUp(object sender, MouseButtonEventArgs e)
        {
            if (_isPanning)
            {
                _isPanning = false;
                ViewportBorder.Cursor = Cursors.Arrow;
                ViewportBorder.ReleaseMouseCapture();
            }
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            ResetViewTransforms();
        }

        private void ResetViewTransforms()
        {
            ViewScaleTransform.ScaleX = 1.0;
            ViewScaleTransform.ScaleY = 1.0;
            ViewTranslateTransform.X = 0.0;
            ViewTranslateTransform.Y = 0.0;
        }

        private void LayoutSelectorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (LayoutSelectorComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string layoutName = selectedItem.Content.ToString();
                LoadLayout(layoutName);
            }
        }

        private void LoadLayout(string layoutName)
        {
            // 1. Clean up previous layout and event subscriptions
            if (_currentLoadedLayout != null)
            {
                if (_currentLoadedLayout == _mainlineLayoutInstance && _mainlineLayoutInstance != null)
                {
                    _mainlineLayoutInstance.ProductExitingLine -= Mainline_ProductExitingLine;
                    System.Diagnostics.Debug.WriteLine("Unsubscribed from MainlineLayout.ProductExitingLine");
                }
                // If _currentLoadedLayout was _inspectionLayoutInstance, no specific events to unsub from in this example
            }

            ZoomPanCanvas.Children.Clear();
            _currentLoadedLayout = null; // Reset general reference
            _mainlineLayoutInstance = null; // Nullify specific instances, they will be re-created if chosen
            _inspectionLayoutInstance = null;

            UserControl layoutToLoad = null;

            // 2. Create and configure new layout
            switch (layoutName)
            {
                case "Mainline":
                    _mainlineLayoutInstance = new MainlineLayout();
                    _mainlineLayoutInstance.ProductExitingLine += Mainline_ProductExitingLine;
                    System.Diagnostics.Debug.WriteLine("Subscribed to MainlineLayout.ProductExitingLine");
                    layoutToLoad = _mainlineLayoutInstance;
                    break;
                case "Inspection":
                    _inspectionLayoutInstance = new InspectionLayout();
                    layoutToLoad = _inspectionLayoutInstance;
                    break;
                default:
                    TextBlock errorText = new TextBlock
                    {
                        Text = $"Layout '{layoutName}' not found.",
                        Foreground = Brushes.Red,
                        FontSize = 16,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    // For centering, it's better if ZoomPanCanvas had a child Grid that does the centering
                    // or set Canvas.Left/Top after measuring, but this is simpler for an error message:
                    ZoomPanCanvas.Children.Add(errorText);
                    // A simple way to somewhat center without complex measurement:
                    errorText.Loaded += (s, e) => {
                        Canvas.SetLeft(errorText, (ZoomPanCanvas.ActualWidth - errorText.ActualWidth) / 2);
                        Canvas.SetTop(errorText, (ZoomPanCanvas.ActualHeight - errorText.ActualHeight) / 2);
                    };
                    break;
            }

            if (layoutToLoad != null)
            {
                _currentLoadedLayout = layoutToLoad; // Store reference to the loaded layout
                ZoomPanCanvas.Children.Add(_currentLoadedLayout);
                ResetViewTransforms(); // Reset view for the new layout
                System.Diagnostics.Debug.WriteLine($"Loaded layout: {layoutName}");
            }
        }

        private void Mainline_ProductExitingLine(object sender, ProductItemEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"Product {e.Product.ProductId} received by MainWindow from Mainline.");

            // Check if the InspectionLayout is currently loaded AND is the _inspectionLayoutInstance we expect
            if (_currentLoadedLayout == _inspectionLayoutInstance && _inspectionLayoutInstance != null)
            {
                _inspectionLayoutInstance.AcceptProduct(e.Product);
                System.Diagnostics.Debug.WriteLine($"Product {e.Product.ProductId} passed to active InspectionLayout.");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"Product {e.Product.ProductId} exited Mainline. InspectionLayout is not the current view or is invalid. Product discarded from UI flow.");
                // Product is effectively lost from the UI simulation at this point if not handled
            }
        }

        public class RelayCommand<T> : ICommand
        {
            private readonly Action<T> _execute;
            private readonly Func<T, bool> _canExecute;

            public event EventHandler CanExecuteChanged
            {
                add { CommandManager.RequerySuggested += value; }
                remove { CommandManager.RequerySuggested -= value; }
            }

            public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
            {
                _execute = execute ?? throw new ArgumentNullException(nameof(execute));
                _canExecute = canExecute;
            }

            public bool CanExecute(object parameter)
            {
                return _canExecute == null || _canExecute((T)parameter);
            }

            public void Execute(object parameter)
            {
                _execute((T)parameter);
            }
        }

        private void UpdateUserInfo()
        {
            if (UserSession.IsLoggedIn)
            {
                UserFullNameTextBlock.Text = UserSession.CurrentUser!.Fullname;
                // Hiển thị ca làm việc với xuống dòng: "Ca hành chính\n8 giờ - 17 giờ"
                var shift = UserSession.CurrentShift!;
                UserShiftTextBlock.Text = $"{shift.Name}\n{shift.TimeRange}";
            }
            else
            {
                UserFullNameTextBlock.Text = "Chưa đăng nhập";
                UserShiftTextBlock.Text = "";
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Bạn có chắc chắn muốn đăng xuất?",
                "Xác nhận đăng xuất",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                UserSession.Logout();

                // Đóng MainWindow và mở LoginWindow
                LoginWindow loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }


    }
}