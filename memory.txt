# MEMORY - LỖI VÀ GIẢI PHÁP

## REQUEST: Fix UI Maintenance Page Issues

### LỖI ĐÃ MẮC PHẢI:

#### 1. **LỖI: Tạo duplicate UI elements**
- **M<PERSON> tả:** Đã tạo 2 set nút "Tổng hợp" và "Trong tháng" trong cùng 1 file XAML
- **Nguyên nhân:** Không kiểm tra kỹ file trước khi thêm elements mới
- **Hậu quả:** UI bị duplicate, confusing cho user
- **Gi<PERSON>i pháp:** 
  - Luôn search existing elements trước khi thêm mới
  - Sử dụng `view` tool với regex để tìm duplicate
  - Xóa elements thừa và update commands cho elements cũ

#### 2. **LỖI: Toast notification null reference**
- **Mô tả:** ShowToast method gọi MainWindow._toastNotification nhưng field bị null
- **Nguyên nhân:** Cố gắng access private field thông qua MainWindow instance
- **Hậ<PERSON> quả:** Runtime exception, toast không hiển thị
- **Gi<PERSON>i pháp:**
  - S<PERSON> dụng `ToastNotificationPanel.Instance.Show()` trực tiếp
  - Wrap trong `Application.Current.Dispatcher.Invoke()` cho thread safety
  - Tránh dependency vào MainWindow internal fields

#### 3. **LỖI: Logic tạo duplicate maintenance plans**
- **Mô tả:** Luôn tạo maintenance plan mới mà không check item đã được maintain chưa
- **Nguyên nhân:** Thiếu validation logic cho maintenance status
- **Hậu quả:** Database bị duplicate records, confusing data
- **Giải pháp:**
  - Check `PerformedBy` field để detect đã maintain chưa
  - Conditional logic: tạo mới nếu chưa maintain, chỉ update nếu đã maintain
  - Different toast messages cho 2 scenarios

#### 4. **LỖI: Không kiểm tra existing code trước khi implement**
- **Mô tả:** Implement features mà không check xem đã có sẵn chưa
- **Nguyên nhân:** Thiếu bước reconnaissance trước khi code
- **Hậu quả:** Duplicate code, wasted effort
- **Giải pháp:**
  - Luôn dùng `view` và `search_query_regex` để check existing code
  - Review file structure trước khi implement
  - Ask user về existing features nếu không chắc

### BEST PRACTICES ĐÃ HỌC:

#### 1. **UI Development:**
- Search existing elements trước khi thêm mới
- Sử dụng consistent naming conventions
- Test UI changes ngay sau khi implement

#### 2. **Error Handling:**
- Luôn wrap external calls trong try-catch
- Sử dụng proper logging cho debugging
- Provide fallback mechanisms

#### 3. **Data Logic:**
- Validate data state trước khi modify
- Implement conditional logic cho different scenarios
- Prevent duplicate data creation

#### 4. **Code Review Process:**
- Check existing codebase trước khi implement
- Use search tools effectively
- Verify changes don't break existing functionality

### TOOLS SỬ DỤNG HIỆU QUẢ:

#### 1. **view tool với regex:**
```
search_query_regex: "Tổng hợp|Trong tháng"
```
- Tìm duplicate elements
- Check existing implementations

#### 2. **str-replace-editor:**
- Edit files incrementally
- Maintain code structure
- Apply consistent formatting

#### 3. **diagnostics:**
- Check compilation errors
- Verify code quality

### WORKFLOW CẢI THIỆN:

#### 1. **Reconnaissance Phase:**
- Survey existing code
- Identify potential conflicts
- Plan implementation strategy

#### 2. **Implementation Phase:**
- Make incremental changes
- Test after each change
- Handle errors immediately

#### 3. **Verification Phase:**
- Build and test
- Verify all requirements met
- Document lessons learned

### COMMIT MESSAGE TEMPLATE:
```
Fix: [Brief description]

Issues resolved:
- Issue 1: Description + Solution
- Issue 2: Description + Solution

Lessons learned:
- Lesson 1
- Lesson 2
```

---
**Date:** 2024-12-19
**Request:** Maintenance Page UI Fixes
**Status:** Completed Successfully
