{"PLC_Real_Test": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "ProductCodeAtReader", "PhysicalAddress": "D4000", "DataType": "STRING", "Length": 10}, {"LogicalName": "SequenceForProductCode", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC1_Reader": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "ProductCodeAtReader", "PhysicalAddress": "D4000", "DataType": "STRING", "Length": 10}, {"LogicalName": "SequenceForProductCode", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC8_Tester": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M1000", "DataType": "BIT", "Length": 1}, {"LogicalName": "TestResultFromTester", "PhysicalAddress": "D500", "DataType": "WORD", "Length": 1}, {"LogicalName": "SequenceNumberAtTest", "PhysicalAddress": "D502", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC_GeneralStatus": [{"LogicalName": "OverallSystemError", "PhysicalAddress": "M2000", "DataType": "BIT", "Length": 1}, {"LogicalName": "EmergencyStopStatus", "PhysicalAddress": "X10", "DataType": "BIT", "Length": 1}]}