using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using ClosedXML.Excel;

namespace ZoomableApp.Services
{
    public class ExcelService
    {
        public ExcelService()
        {
            // ClosedXML is completely free - no license required!
        }

        /// <summary>
        /// Đọc file Excel và trả về DataTable
        /// </summary>
        /// <param name="filePath">Đường dẫn đến file Excel</param>
        /// <param name="worksheetIndex">Index của worksheet (mặc định là 0)</param>
        /// <returns>DataTable chứa dữ liệu từ Excel</returns>
        public DataTable ReadExcelToDataTable(string filePath, int worksheetIndex = 0)
        {
            var dataTable = new DataTable();

            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File không tồn tại: {filePath}");
                }

                using (var workbook = new XLWorkbook(filePath))
                {
                    if (workbook.Worksheets.Count == 0)
                    {
                        throw new InvalidOperationException("File Excel không có worksheet nào.");
                    }

                    var worksheet = workbook.Worksheets.Worksheet(worksheetIndex + 1); // ClosedXML uses 1-based indexing

                    if (worksheet == null)
                    {
                        throw new InvalidOperationException($"Worksheet tại index {worksheetIndex} không tồn tại.");
                    }

                    var range = worksheet.RangeUsed();
                    if (range == null)
                    {
                        throw new InvalidOperationException("Worksheet trống hoặc không có dữ liệu.");
                    }

                    // Lấy range dữ liệu
                    var startRow = range.FirstRow().RowNumber();
                    var endRow = range.LastRow().RowNumber();
                    var startCol = range.FirstColumn().ColumnNumber();
                    var endCol = range.LastColumn().ColumnNumber();

                    // Tạo columns từ header row (row đầu tiên)
                    for (int col = startCol; col <= endCol; col++)
                    {
                        var headerValue = worksheet.Cell(startRow, col).GetString();
                        if (string.IsNullOrEmpty(headerValue))
                            headerValue = $"Column{col}";
                        dataTable.Columns.Add(headerValue);
                    }

                    // Thêm data rows (bỏ qua header row)
                    for (int row = startRow + 1; row <= endRow; row++)
                    {
                        var dataRow = dataTable.NewRow();

                        for (int col = startCol; col <= endCol; col++)
                        {
                            var cell = worksheet.Cell(row, col);
                            string cellValue;

                            // Xử lý đặc biệt để giữ leading zeros
                            if (cell.HasFormula)
                            {
                                cellValue = cell.CachedValue.ToString() ?? string.Empty;
                            }
                            else if (cell.DataType == XLDataType.Text)
                            {
                                cellValue = cell.GetString();
                            }
                            else if (cell.DataType == XLDataType.Number)
                            {
                                // Kiểm tra nếu cell được format như text hoặc có leading zeros
                                var formatCode = cell.Style.NumberFormat.Format;
                                if (formatCode.Contains("@") || formatCode.Contains("0000"))
                                {
                                    // Giữ nguyên format với leading zeros
                                    cellValue = cell.GetFormattedString();
                                }
                                else
                                {
                                    cellValue = cell.GetString();
                                }
                            }
                            else
                            {
                                cellValue = cell.GetString();
                            }

                            dataRow[col - startCol] = cellValue ?? string.Empty;
                        }

                        dataTable.Rows.Add(dataRow);
                    }

                    System.Diagnostics.Debug.WriteLine($"Excel loaded successfully: {dataTable.Rows.Count} rows, {dataTable.Columns.Count} columns");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading Excel file: {ex.Message}");
                throw;
            }

            return dataTable;
        }

        /// <summary>
        /// Lấy danh sách tên các worksheet trong file Excel
        /// </summary>
        /// <param name="filePath">Đường dẫn đến file Excel</param>
        /// <returns>List tên các worksheet</returns>
        public List<string> GetWorksheetNames(string filePath)
        {
            var worksheetNames = new List<string>();

            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File không tồn tại: {filePath}");
                }

                using (var workbook = new XLWorkbook(filePath))
                {
                    foreach (var worksheet in workbook.Worksheets)
                    {
                        worksheetNames.Add(worksheet.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting worksheet names: {ex.Message}");
                throw;
            }

            return worksheetNames;
        }

        /// <summary>
        /// Kiểm tra file Excel có hợp lệ không
        /// </summary>
        /// <param name="filePath">Đường dẫn đến file Excel</param>
        /// <returns>True nếu file hợp lệ</returns>
        public bool IsValidExcelFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".xlsx" && extension != ".xls")
                    return false;

                using (var workbook = new XLWorkbook(filePath))
                {
                    return workbook.Worksheets.Count > 0;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Ghi DataTable vào file Excel
        /// </summary>
        /// <param name="dataTable">DataTable chứa dữ liệu</param>
        /// <param name="filePath">Đường dẫn file Excel</param>
        /// <param name="worksheetName">Tên worksheet (mặc định là "Sheet1")</param>
        /// <param name="overwrite">Có ghi đè file không (mặc định là true)</param>
        public void WriteDataTableToExcel(DataTable dataTable, string filePath, string worksheetName = "Sheet1", bool overwrite = true)
        {
            try
            {
                // Tạo thư mục nếu chưa tồn tại
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                XLWorkbook workbook;

                if (File.Exists(filePath) && !overwrite)
                {
                    // Mở file hiện có
                    workbook = new XLWorkbook(filePath);

                    // Xóa worksheet cũ nếu tồn tại
                    if (workbook.Worksheets.Contains(worksheetName))
                    {
                        workbook.Worksheets.Delete(worksheetName);
                    }
                }
                else
                {
                    // Tạo workbook mới
                    workbook = new XLWorkbook();
                }

                // Thêm worksheet mới
                var worksheet = workbook.Worksheets.Add(worksheetName);

                // Ghi headers
                for (int col = 0; col < dataTable.Columns.Count; col++)
                {
                    worksheet.Cell(1, col + 1).Value = dataTable.Columns[col].ColumnName;
                    worksheet.Cell(1, col + 1).Style.Font.Bold = true;
                    worksheet.Cell(1, col + 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                }

                // Ghi dữ liệu
                for (int row = 0; row < dataTable.Rows.Count; row++)
                {
                    for (int col = 0; col < dataTable.Columns.Count; col++)
                    {
                        var cellValue = dataTable.Rows[row][col];
                        worksheet.Cell(row + 2, col + 1).Value = cellValue?.ToString() ?? "";
                    }
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                // Lưu file
                workbook.SaveAs(filePath);
                workbook.Dispose();

                System.Diagnostics.Debug.WriteLine($"ExcelService: Successfully wrote data to {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ExcelService: Error writing to Excel: {ex.Message}");
                throw;
            }
        }
    }
}
