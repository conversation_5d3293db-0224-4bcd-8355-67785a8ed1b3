using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    public class HomeDailyPlanViewModel : INotifyPropertyChanged
    {
        private readonly ExcelService _excelService;
        private readonly ExcelSettings _excelSettings;
        private ObservableCollection<DailyPlanItem> _dailyPlanItems;
        private DailyPlanItem _selectedItem;
        private DailyPlanItem _currentWorkingItem;
        private string _todayDateText = "";
        private string _currentProductText = "";

        public HomeDailyPlanViewModel()
        {
            _excelService = new ExcelService();
            _excelSettings = ConfigLoader.LoadExcelSettings();
            _dailyPlanItems = new ObservableCollection<DailyPlanItem>();
            
            // Commands
            RefreshCommand = new RelayCommand(LoadDailyPlan);
            MarkCompleteCommand = new RelayCommand(MarkCurrentComplete, CanMarkComplete);
            StartNextCommand = new RelayCommand(StartNextItem, CanStartNext);
            
            // Set today date
            TodayDateText = DateTime.Now.ToString("dd/MM/yyyy");
            
            // Load initial data
            LoadDailyPlan();
        }

        #region Properties

        public ObservableCollection<DailyPlanItem> DailyPlanItems
        {
            get => _dailyPlanItems;
            set
            {
                _dailyPlanItems = value;
                OnPropertyChanged();
            }
        }

        public DailyPlanItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public DailyPlanItem CurrentWorkingItem
        {
            get => _currentWorkingItem;
            set
            {
                _currentWorkingItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                _todayDateText = value;
                OnPropertyChanged();
            }
        }

        public string CurrentProductText
        {
            get => _currentProductText;
            set
            {
                _currentProductText = value;
                OnPropertyChanged();
            }
        }

        public ICommand RefreshCommand { get; }
        public ICommand MarkCompleteCommand { get; }
        public ICommand StartNextCommand { get; }

        #endregion

        #region Methods

        private void LoadDailyPlan()
        {
            try
            {
                if (!_excelService.IsValidExcelFile(_excelSettings.PlanFilePath))
                {
                    DailyPlanItems.Clear();
                    return;
                }

                DataTable excelData = _excelService.ReadExcelToDataTable(_excelSettings.PlanFilePath);
                var dailyData = ExtractDailyPlan(excelData);
                
                DailyPlanItems.Clear();
                
                foreach (DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "",
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };
                    
                    DailyPlanItems.Add(item);
                }

                // Set first item as current working if no current item
                if (CurrentWorkingItem == null && DailyPlanItems.Count > 0)
                {
                    CurrentWorkingItem = DailyPlanItems.First();
                    CurrentWorkingItem.Status = PlanItemStatus.InProgress;
                }

                System.Diagnostics.Debug.WriteLine($"Loaded {DailyPlanItems.Count} daily plan items");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading daily plan: {ex.Message}");
            }
        }

        private DataTable ExtractDailyPlan(DataTable monthlyData)
        {
            if (monthlyData == null || monthlyData.Rows.Count == 0)
                return new DataTable();

            // Find Date column
            DataColumn dateColumn = null;
            foreach (DataColumn column in monthlyData.Columns)
            {
                if (column.ColumnName.ToLower().Contains("date") || 
                    column.ColumnName.ToLower().Contains("ngày"))
                {
                    dateColumn = column;
                    break;
                }
            }

            if (dateColumn == null)
                return new DataTable();

            // Create new DataTable with same structure
            DataTable dailyTable = monthlyData.Clone();
            
            // Get today's date
            string today = DateTime.Now.ToString("dd/MM");
            string todayFull = DateTime.Now.ToString("dd/MM/yyyy");
            
            // Filter rows matching today
            foreach (DataRow row in monthlyData.Rows)
            {
                string dateValue = row[dateColumn].ToString();
                
                if (!string.IsNullOrEmpty(dateValue) && IsDateMatch(dateValue, today, todayFull))
                {
                    dailyTable.ImportRow(row);
                }
            }

            return dailyTable;
        }

        private bool IsDateMatch(string dateValue, string today, string todayFull)
        {
            try
            {
                if (DateTime.TryParseExact(dateValue, "dd/MM/yyyy", null, DateTimeStyles.None, out DateTime parsedDate))
                {
                    return parsedDate.ToString("dd/MM") == today;
                }
                
                if (DateTime.TryParseExact($"{dateValue}/{DateTime.Now.Year}", "dd/MM/yyyy", null, DateTimeStyles.None, out parsedDate))
                {
                    return parsedDate.ToString("dd/MM") == today;
                }
                
                if (DateTime.TryParse(dateValue, out parsedDate))
                {
                    return parsedDate.ToString("dd/MM") == today;
                }
                
                return dateValue.Contains(today) || dateValue.Contains(todayFull);
            }
            catch
            {
                return false;
            }
        }

        private void UpdateCurrentProductText()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentProductText = CurrentWorkingItem.ModelName;
            }
            else
            {
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        private void MarkCurrentComplete()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentWorkingItem.Status = PlanItemStatus.Completed;
                CurrentWorkingItem = null;
                UpdateCurrentProductText();
            }
        }

        private bool CanMarkComplete()
        {
            return CurrentWorkingItem != null && CurrentWorkingItem.Status == PlanItemStatus.InProgress;
        }

        private void StartNextItem()
        {
            if (SelectedItem != null && SelectedItem.Status == PlanItemStatus.NotStarted)
            {
                SelectedItem.Status = PlanItemStatus.InProgress;
                CurrentWorkingItem = SelectedItem;
                SelectedItem = null;
            }
        }

        private bool CanStartNext()
        {
            return SelectedItem != null && 
                   SelectedItem.Status == PlanItemStatus.NotStarted && 
                   CurrentWorkingItem == null;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    public class DailyPlanItem : INotifyPropertyChanged
    {
        private PlanItemStatus _status;

        public string No { get; set; } = "";
        public string Type { get; set; } = "";
        public string ModelName { get; set; } = "";
        public string Market { get; set; } = "";
        public string Quantity { get; set; } = "";
        public string StartTime { get; set; } = "";
        public string StopTime { get; set; } = "";

        public PlanItemStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum PlanItemStatus
    {
        NotStarted,    // Chưa bắt đầu - màu nền bình thường
        Selected,      // Được chọn - màu xanh dương  
        InProgress,    // Đang làm - màu vàng
        Completed      // Hoàn thành - màu xanh lá
    }
}
