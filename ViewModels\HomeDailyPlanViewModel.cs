using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    public class HomeDailyPlanViewModel : INotifyPropertyChanged
    {
        private readonly PlanViewModel _planViewModel;
        private ObservableCollection<DailyPlanItem> _dailyPlanItems;
        private DailyPlanItem _selectedItem;
        private DailyPlanItem _currentWorkingItem;
        private string _todayDateText = "";
        private string _currentProductText = "";

        public HomeDailyPlanViewModel(PlanViewModel planViewModel = null)
        {
            _planViewModel = planViewModel ?? new PlanViewModel();
            _dailyPlanItems = new ObservableCollection<DailyPlanItem>();

            // Commands
            RefreshCommand = new RelayCommand(LoadDailyPlan);
            MarkCompleteCommand = new RelayCommand(MarkCurrentComplete, CanMarkComplete);
            StartNextCommand = new RelayCommand(StartNextItem, CanStartNext);

            // Set today date
            TodayDateText = DateTime.Now.ToString("dd/MM/yyyy");

            // Subscribe to PlanViewModel changes
            _planViewModel.PropertyChanged += PlanViewModel_PropertyChanged;

            // Load initial data
            LoadDailyPlan();
        }

        #region Properties

        public ObservableCollection<DailyPlanItem> DailyPlanItems
        {
            get => _dailyPlanItems;
            set
            {
                _dailyPlanItems = value;
                OnPropertyChanged();
            }
        }

        public DailyPlanItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public DailyPlanItem CurrentWorkingItem
        {
            get => _currentWorkingItem;
            set
            {
                _currentWorkingItem = value;
                OnPropertyChanged();
                UpdateCurrentProductText();
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                _todayDateText = value;
                OnPropertyChanged();
            }
        }

        public string CurrentProductText
        {
            get => _currentProductText;
            set
            {
                _currentProductText = value;
                OnPropertyChanged();
            }
        }

        public ICommand RefreshCommand { get; }
        public ICommand MarkCompleteCommand { get; }
        public ICommand StartNextCommand { get; }

        #endregion

        #region Methods

        private void PlanViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PlanViewModel.DailyPlanData))
            {
                System.Diagnostics.Debug.WriteLine("HomeDailyPlan: PlanViewModel DailyPlanData changed, refreshing...");
                LoadDailyPlanFromPlanViewModel();
            }
        }

        private void LoadDailyPlan()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("HomeDailyPlan: LoadDailyPlan called - refreshing PlanViewModel");

                // Refresh PlanViewModel data first
                if (_planViewModel.RefreshCommand.CanExecute(null))
                {
                    _planViewModel.RefreshCommand.Execute(null);
                }

                // Then load from PlanViewModel
                LoadDailyPlanFromPlanViewModel();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HomeDailyPlan: Error in LoadDailyPlan: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Lỗi tải dữ liệu";
            }
        }

        private void LoadDailyPlanFromPlanViewModel()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("HomeDailyPlan: LoadDailyPlanFromPlanViewModel called");

                var dailyData = _planViewModel.DailyPlanData;
                if (dailyData == null || dailyData.Rows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("HomeDailyPlan: No daily data available from PlanViewModel");
                    DailyPlanItems.Clear();
                    CurrentProductText = "Không có kế hoạch hôm nay";
                    return;
                }
                
                DailyPlanItems.Clear();
                
                foreach (DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "",
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };
                    
                    DailyPlanItems.Add(item);
                }

                // Update current product if we have items
                if (DailyPlanItems.Count > 0)
                {
                    var firstItem = DailyPlanItems.FirstOrDefault();
                    CurrentProductText = firstItem?.ModelName ?? "Không xác định";

                    // Set first item as current working if no current item
                    if (CurrentWorkingItem == null)
                    {
                        CurrentWorkingItem = DailyPlanItems.First();
                        CurrentWorkingItem.Status = PlanItemStatus.InProgress;
                    }
                }
                else
                {
                    CurrentProductText = "Không có kế hoạch";
                }

                System.Diagnostics.Debug.WriteLine($"HomeDailyPlan: Loaded {DailyPlanItems.Count} items from PlanViewModel daily data");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HomeDailyPlan: Error loading daily plan from PlanViewModel: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Lỗi tải dữ liệu";
            }
        }



        private void UpdateCurrentProductText()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentProductText = CurrentWorkingItem.ModelName;
            }
            else
            {
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        private void MarkCurrentComplete()
        {
            if (CurrentWorkingItem != null)
            {
                CurrentWorkingItem.Status = PlanItemStatus.Completed;
                CurrentWorkingItem = null;
                UpdateCurrentProductText();
            }
        }

        private bool CanMarkComplete()
        {
            return CurrentWorkingItem != null && CurrentWorkingItem.Status == PlanItemStatus.InProgress;
        }

        private void StartNextItem()
        {
            if (SelectedItem != null && SelectedItem.Status == PlanItemStatus.NotStarted)
            {
                SelectedItem.Status = PlanItemStatus.InProgress;
                CurrentWorkingItem = SelectedItem;
                SelectedItem = null;
            }
        }

        private bool CanStartNext()
        {
            return SelectedItem != null && 
                   SelectedItem.Status == PlanItemStatus.NotStarted && 
                   CurrentWorkingItem == null;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    public class DailyPlanItem : INotifyPropertyChanged
    {
        private PlanItemStatus _status;

        public string No { get; set; } = "";
        public string Type { get; set; } = "";
        public string ModelName { get; set; } = "";
        public string Market { get; set; } = "";
        public string Quantity { get; set; } = "";
        public string StartTime { get; set; } = "";
        public string StopTime { get; set; } = "";

        public PlanItemStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum PlanItemStatus
    {
        NotStarted,    // Chưa bắt đầu - màu nền bình thường
        Selected,      // Được chọn - màu xanh dương  
        InProgress,    // Đang làm - màu vàng
        Completed      // Hoàn thành - màu xanh lá
    }
}
