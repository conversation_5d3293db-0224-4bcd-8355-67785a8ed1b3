using Microsoft.Data.Sqlite;
using System.IO;

namespace ZoomableApp.Services
{
    public static class DatabaseInitializer
    {
        public static async Task InitializeDatabaseAsync()
        {
            var dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }

            var dbPath = Path.Combine(dataDirectory, "panaDB.db");
            var connectionString = $"Data Source={dbPath}";

            try
            {
                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync();

                // Tạo bảng users
                var createTableCommand = connection.CreateCommand();
                createTableCommand.CommandText = @"
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER NOT NULL UNIQUE,
                        username TEXT,
                        password TEXT,
                        role TEXT,
                        fullname TEXT,
                        PRIMARY KEY(id AUTOINCREMENT)
                    )";
                await createTableCommand.ExecuteNonQueryAsync();

                // Ki<PERSON>m tra xem đã có dữ liệu chưa
                var checkDataCommand = connection.CreateCommand();
                checkDataCommand.CommandText = "SELECT COUNT(*) FROM users";
                var count = Convert.ToInt32(await checkDataCommand.ExecuteScalarAsync());

                if (count == 0)
                {
                    // Thêm dữ liệu mẫu
                    var insertCommand = connection.CreateCommand();
                    insertCommand.CommandText = @"
                        INSERT INTO users (username, password, role, fullname) VALUES 
                        ('admin', 'admin123', 'Administrator', 'Quản trị viên'),
                        ('operator1', 'op123', 'Operator', 'Nguyễn Văn A'),
                        ('operator2', 'op456', 'Operator', 'Trần Thị B'),
                        ('supervisor', 'sup789', 'Supervisor', 'Lê Văn C'),
                        ('technician', 'tech123', 'Technician', 'Phạm Thị D')";
                    await insertCommand.ExecuteNonQueryAsync();

                    System.Diagnostics.Debug.WriteLine("Database initialized with sample data.");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing database: {ex.Message}");
            }
        }
    }
}
