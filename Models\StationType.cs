﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public enum StationType
    {
        None, // Mặc định
        Lifter,
        Crane,      // <PERSON><PERSON><PERSON> trục
        WorkerRoller, // Con lăn cho công nhân làm việc
        TestRoller,   // Con lăn máy test
        StandardRoller, // Con lăn tiêu chuẩn
        ReworkRoller  // Con lăn có nhánh rework
    }

    public enum TestResultStatus
    {
        None,           // Không có thông tin/Không áp dụng
        AwaitingProduct,// Trạm test đang chờ sản phẩm
        Testing,        // Đang trong quá trình test
        OK,
        NG
    }

    public enum DeviceOperationalStatus
    {
        None,           // Không có thông tin
        Idle,           // Đang rảnh, sẵn sàng
        Running,        // Đang hoạt động
        Error,          // Lỗi thiết bị
        Off,            // Tắt
        Maintenance     // Đang bảo trì
    }
}
