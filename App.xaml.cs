﻿using System.Configuration;
using System.Data;
using System.Windows;
using ZoomableApp.Services;

namespace ZoomableApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // Không tự động khởi tạo database - sử dụng database có sẵn trong thư mục Data
        // await DatabaseInitializer.InitializeDatabaseAsync();

        // Tạo và hiển thị LoginWindow
        LoginWindow loginWindow = new LoginWindow();
        loginWindow.Show();
    }
}

