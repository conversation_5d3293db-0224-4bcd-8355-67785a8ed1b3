﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public enum PlcDeviceAddress
    {
        BitTest,
        ProductCodeAtReader,
        // ---- Trạng thái chung ----
        SystemReadyBit,         // Bit báo hệ thống PLC sẵn sàng
        SystemErrorBit,         // Bit báo lỗi hệ thống chung
        EmergencyStopBit,       // Bit báo dừng khẩn cấp

        // ---- <PERSON>h ghi phát hiện vị trí có hàng (cho từng trạm) ----
        // <PERSON><PERSON><PERSON> sử có tối đa 30 trạm, mỗi trạm 1 bit
        Station01_ProductPresenceBit,
        Station02_ProductPresenceBit,
        // ... (tương tự cho các trạm khác)
        Station30_ProductPresenceBit,

        // ---- Thanh ghi trạng thái test OK/NG (cho từng trạm test) ----
        // <PERSON><PERSON><PERSON> sử các trạm test có thanh ghi riêng để lưu kết quả (0=None, 1=OK, 2=NG)
        TestStation09_ResultWord, // Trạm 09
        TestStation10_ResultWord, // Trạm 10
        // ... (tương tự cho các trạm test khác)
        TestStation15_ResultWord,

        // ---- Thanh ghi thời gian thao tác (cho từng trạm công nhân) ----
        // Lưu dưới dạng WORD (ví dụ: số giây)
        WorkerStation03_OperationTimeWord,
        WorkerStation04_OperationTimeWord,
        // ... (tương tự cho các trạm công nhân khác)
        WorkerStation08_OperationTimeWord,

        // ---- Thanh ghi trạng thái thiết bị (cho từng trạm/thiết bị quan trọng) ----
        // (0=None, 1=Idle, 2=Running, 3=Error, 4=Off, 5=Maintenance)
        Device_Lifter_StatusWord,
        Device_Crane_StatusWord,
        Device_TestMachineVoltage1_StatusWord,
        Device_TestMachineCurrentLeak_StatusWord,
        // ... (có thể có nhiều thanh ghi trạng thái thiết bị hơn)

        // ---- Thanh ghi lưu mã sản phẩm (cho các vị trí đọc mã/test) ----
        // Giả sử mã sản phẩm là string, lưu trong nhiều WORD liên tiếp.
        // Chúng ta sẽ cần địa chỉ bắt đầu và độ dài.
        ProductCode_ReaderStation_StartWord, // Vị trí đọc mã đầu vào
        ProductCode_TestStation09_StartWord, // Mã sản phẩm tại trạm test 09
        // ...

        // ---- Thanh ghi lưu số thứ tự sản phẩm ----
        // Có thể là một WORD hoặc DWORD
        CurrentProductSequenceNumberWord, // Số thứ tự của sản phẩm đang được xử lý chung
        TestStation09_ProductSequenceNumberWord, // Số thứ tự tại trạm test 09 (nếu cần riêng)

        // ---- Các thanh ghi điều khiển/phản hồi khác (ví dụ) ----
        ConveyorStartStopBit,       // Bit điều khiển băng chuyền
        ConveyorSpeedSetWord,       // Thanh ghi cài đặt tốc độ
        ConveyorActualSpeedWord,    // Thanh ghi tốc độ thực tế
    }

    public enum PlcDataType
    {
        BIT,
        WORD,       // 16-bit signed/unsigned
        DWORD,      // 32-bit signed/unsigned
        FLOAT,      // Single-precision float
        STRING      // Chuỗi ký tự, thường lưu trong nhiều WORD
    }
}
