﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using ZoomableApp.Services;

namespace ZoomableApp.ViewModels
{
    public class PlcConnectionUIData : INotifyPropertyChanged
    {
        private string _valueToWrite = "123"; // Giá trị mặc định để ghi (sẽ được parse sang short)
        public string ValueToWrite
        {
            get => _valueToWrite;
            set { _valueToWrite = value; OnPropertyChanged(); }
        }

        private string _readValue = ""; // Giá trị đọc được (hiển thị)
        public string ReadValue
        {
            get => _readValue;
            set { _readValue = value; OnPropertyChanged(); }
        }
        private string _plcId;
        public string PlcId
        {
            get => _plcId;
            set { _plcId = value; OnPropertyChanged(); }
        }

        private string _name;
        public string Name
        {
            get => _name;
            set { _name = value; OnPropertyChanged(); }
        }


        private string _ipAddress = "************"; // <PERSON>i<PERSON> trị mặc định
        public string IpAddress
        {
            get => _ipAddress;
            set { _ipAddress = value; OnPropertyChanged(); }
        }

        private int _port = 5007; // Giá trị mặc định
        public int Port
        {
            get => _port;
            set { _port = value; OnPropertyChanged(); }
        }

        private bool _isConnected;
        public bool IsConnected
        {
            get => _isConnected;
            set { _isConnected = value; OnPropertyChanged(); }
        }

        private string _connectionStatus = "Not Connected";
        public string ConnectionStatus
        {
            get => _connectionStatus;
            set { _connectionStatus = value; OnPropertyChanged(); }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Constructor để ánh xạ từ PlcConnectionInfo của Service
        public PlcConnectionUIData() { } // Constructor mặc định cho XAML designer
        public PlcConnectionUIData(PlcConnectionInfo serviceInfo)
        {
            PlcId = serviceInfo.Id;
            Name = serviceInfo.Name;
            IpAddress = serviceInfo.IpAddress;
            Port = serviceInfo.Port;
            IsConnected = serviceInfo.IsConnected;
            ConnectionStatus = serviceInfo.ConnectionStatus;
        }
        // Method để cập nhật từ Service Info
        public void UpdateFromServiceInfo(PlcConnectionInfo serviceInfo)
        {
            // Chỉ cập nhật trạng thái, không cập nhật IP/Port vì người dùng có thể đang sửa
            IsConnected = serviceInfo.IsConnected;
            ConnectionStatus = serviceInfo.ConnectionStatus;
        }
    }
}