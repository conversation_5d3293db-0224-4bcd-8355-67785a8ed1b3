﻿<UserControl x:Class="ZoomableApp.SharedControls.MySharedElement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp.SharedControls"
             mc:Ignorable="d" 
             d:DesignHeight="100" d:DesignWidth="200">
    <Border BorderBrush="Green" BorderThickness="1" Padding="5" Background="LightGoldenrodYellow">
        <StackPanel>
            <TextBlock Text="{Binding ElementName=ucRoot, Path=ElementNameText}" FontWeight="Bold"/>
            <Button Content="Shared Action" Margin="0,5,0,0"/>
            <Ellipse Fill="MediumPurple" Width="50" Height="30" Margin="0,5,0,0"/>
        </StackPanel>
    </Border>
</UserControl>
