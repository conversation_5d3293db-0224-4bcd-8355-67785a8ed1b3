﻿<Application x:Class="ZoomableApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ZoomableApp"
             xmlns:converters="clr-namespace:ZoomableApp.Converters"
             >
    <Application.Resources>
        <ResourceDictionary>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter"/>
            <converters:GreaterThanToVisibilityConverter x:Key="GreaterThanToVisibilityConverter"/>
            <converters:StringContainsFailedConverter x:Key="StringContainsFailedConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
