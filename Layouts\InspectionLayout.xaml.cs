﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using ZoomableApp.Models;
using ZoomableApp.SharedControls;

namespace ZoomableApp.Layouts
{
    public partial class InspectionLayout : UserControl
    {
        private List<StationControl> _stations = new List<StationControl>();
        private ObservableCollection<ProductItem> _activeProductsData = new ObservableCollection<ProductItem>();
        private Dictionary<string, ProductVisualControl> _productVisuals = new Dictionary<string, ProductVisualControl>();
        private DispatcherTimer _simulationTimer;
        private List<double> _stationCenterXVals = new List<double>();

        // Scale factor: mm in drawing to UI pixels. e.g., 1000mm -> 100px
        private const double MM_TO_PX_SCALE = 1.0 / 10.0;

        public InspectionLayout()
        {
            InitializeComponent();
            Loaded += InspectionLayout_Loaded;
            Unloaded += InspectionLayout_Unloaded;
        }

        private void InspectionLayout_Loaded(object sender, RoutedEventArgs e)
        {
            InitializeStations();
            // Defer CalculateStationPositions until layout is surely done
            Dispatcher.BeginInvoke(new Action(() => {
                CalculateStationPositions();
            }), DispatcherPriority.ContextIdle);


            _simulationTimer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(5) }; // "Done" signal every 5s
            _simulationTimer.Tick += SimulationTimer_Tick;
            _simulationTimer.Start();
        }

        private void InspectionLayout_Unloaded(object sender, RoutedEventArgs e)
        {
            _simulationTimer?.Stop();
            ProductLayerCanvas.Children.Clear();
            _productVisuals.Clear();
            _activeProductsData.Clear(); // Clear data when unloaded
            _stations.Clear();
            StationStackPanel.Children.Clear();
        }

        private void InitializeStations()
        {
            StationStackPanel.Children.Clear();
            _stations.Clear();

            // Station definitions based on the drawing (27-62)
            // Segment lengths from drawing:
            // S1: 5399mm (St 27-32, 6 stations) -> ~899.83 mm/st
            // S2: 5399mm (St 33-38, 6 stations) -> ~899.83 mm/st
            // S3: 3570mm (St 39-42, 4 stations) ->  892.50 mm/st
            // S4: 6259mm (St 43-48, 6 stations) -> ~1043.17 mm/st (Buồng cách âm)
            // S5: 5669mm (St 49-54, 6 stations) -> ~944.83 mm/st
            // S6: 1500mm (St 55, 1 station - Camera)
            // S7: 1500mm (St 56, 1 station - Roller)
            // S8: 2700mm (St 57-58, 2 stations - Máy gấp, Lifter) -> 1350 mm/st
            // S9: 3960mm (St 59-62, 4 stations - Rework area) -> 990 mm/st

            var stationData = new List<Tuple<string, double, StationType, string>> // Name, Length_mm, Type, Description
            {
                // Segment 1 (Cầu thang, Máy kiểm tra độ rung)
                Tuple.Create("27", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("28", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("29", 899.83, StationType.StandardRoller, "Roller near Vibration Test"),
                Tuple.Create("30", 899.83, StationType.TestRoller, "Vibration Test"), // Máy kiểm tra độ rung
                Tuple.Create("31", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("32", 899.83, StationType.StandardRoller, "Roller"),
                // Segment 2 (Rollers)
                Tuple.Create("33", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("34", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("35", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("36", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("37", 899.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("38", 899.83, StationType.StandardRoller, "Roller"),
                // Segment 3 (Máy in tem)
                Tuple.Create("39", 892.50, StationType.StandardRoller, "Entry to Print"),
                Tuple.Create("40", 892.50, StationType.WorkerRoller, "IoT/Warranty Label Print"), // Máy in tem
                Tuple.Create("41", 892.50, StationType.WorkerRoller, "Label Print Aux"),
                Tuple.Create("42", 892.50, StationType.StandardRoller, "Exit Print"),
                // Segment 4 (Buồng cách âm)
                Tuple.Create("43", 1043.17, StationType.TestRoller, "Soundproof Test 1"),
                Tuple.Create("44", 1043.17, StationType.TestRoller, "Soundproof Test 2"),
                Tuple.Create("45", 1043.17, StationType.TestRoller, "Soundproof Test 3"),
                Tuple.Create("46", 1043.17, StationType.TestRoller, "Soundproof Test 4"),
                Tuple.Create("47", 1043.17, StationType.TestRoller, "Soundproof Test 5"),
                Tuple.Create("48", 1043.17, StationType.TestRoller, "Soundproof Test 6 / Exit"),
                // Segment 5 (Máy test điện cao áp, Turntable)
                Tuple.Create("49", 944.83, StationType.StandardRoller, "Entry to HV Test"),
                Tuple.Create("50", 944.83, StationType.StandardRoller, "Roller"),
                Tuple.Create("51", 944.83, StationType.TestRoller, "High Voltage Test Prep"), // Máy test điện cao áp
                Tuple.Create("52", 944.83, StationType.TestRoller, "High Voltage Test"),
                Tuple.Create("53", 944.83, StationType.StandardRoller, "Exit HV Test"),
                Tuple.Create("54", 944.83, StationType.StandardRoller, "Turntable"), // Turntable (treat as roller for now)
                // Segment 6 (Camera)
                Tuple.Create("55", 1500.0, StationType.TestRoller, "Camera Inspection"), // Camera
                // Segment 7 (Roller)
                Tuple.Create("56", 1500.0, StationType.StandardRoller, "Roller to Folding"),
                // Segment 8 (Máy gấp, Cấp xốp, Lifter)
                Tuple.Create("57", 1350.0, StationType.WorkerRoller, "Folding Machine / Foam Supply"), // Máy gấp + Cấp xốp
                Tuple.Create("58", 1350.0, StationType.Lifter, "Lifter"), // Lifter
                // Segment 9 (Rollers, Rework)
                Tuple.Create("59", 990.0, StationType.StandardRoller, "Roller after Lifter"),
                Tuple.Create("60", 990.0, StationType.StandardRoller, "Roller"),
                Tuple.Create("61", 990.0, StationType.ReworkRoller, "Rework Station"), // Rework
                Tuple.Create("62", 990.0, StationType.StandardRoller, "Final Exit Roller")
            };

            foreach (var data in stationData)
            {
                var station = new StationControl
                {
                    StationNumber = data.Item1,
                    StationLength = data.Item2 * MM_TO_PX_SCALE,
                    StationType = data.Item3,
                    ToolTip = data.Item4, // Add tooltip for description
                    DeviceStatus = DeviceOperationalStatus.Idle
                };
                _stations.Add(station);
                StationStackPanel.Children.Add(station);
            }
            UpdateLayout(); // Ensure stations are rendered for ActualWidth
        }

        private void CalculateStationPositions()
        {
            _stationCenterXVals.Clear();
            if (_stations.Count == 0) return;

            StationStackPanel.UpdateLayout(); // Ensure ActualWidth is up-to-date

            double currentXOffset = 0;
            foreach (var station in _stations)
            {
                if (station.ActualWidth == 0)
                {
                    // Fallback if ActualWidth is still 0 after UpdateLayout (should be rare if called correctly)
                    _stationCenterXVals.Add(currentXOffset + (station.StationLength / 2.0));
                    currentXOffset += station.StationLength;
                }
                else
                {
                    _stationCenterXVals.Add(currentXOffset + station.ActualWidth / 2.0);
                    currentXOffset += station.ActualWidth;
                }
            }
            // Set Canvas Width to match StackPanel
            ProductLayerCanvas.Width = currentXOffset;
        }

        public void AcceptProduct(ProductItem productData)
        {
            if (_activeProductsData.Any(p => p.ProductId == productData.ProductId))
            {
                System.Diagnostics.Debug.WriteLine($"Product {productData.ProductId} already in inspection line. Ignoring.");
                return;
            }
            // Reset station index, it will be set by AddProductVisualToLine
            productData.CurrentStationIndex = -1;
            // Reset test result for the inspection phase if needed
            productData.CurrentTestResult = TestResultStatus.AwaitingProduct;

            _activeProductsData.Add(productData);
            AddProductVisualToLine(productData, true); // Animate from entry point
        }

        private void AddProductVisualToLine(ProductItem productData, bool animateFromEntryPoint = false)
        {
            var productVisual = new ProductVisualControl { DataContext = productData };
            _productVisuals[productData.ProductId] = productVisual;
            ProductLayerCanvas.Children.Add(productVisual);

            if (_stations.Count == 0 || _stationCenterXVals.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("Error: Stations not initialized or positions not calculated in InspectionLayout.");
                // Clean up if setup fails
                ProductLayerCanvas.Children.Remove(productVisual);
                _productVisuals.Remove(productData.ProductId);
                _activeProductsData.Remove(productData);
                return;
            }

            productData.CurrentStationIndex = 0; // Target the first station of inspection line
            var targetStation = _stations[0];
            targetStation.HasProduct = true;
            if (targetStation.IsTestStationType)
            {
                targetStation.ProductStatusAtStation = productData.CurrentTestResult; // Will be Awaiting or Testing
            }
            targetStation.UpdateStationDisplayBrush();

            productVisual.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
            double visualWidth = productVisual.DesiredSize.Width;
            double visualHeight = productVisual.DesiredSize.Height;

            // If canvas height is not set yet, try to get it from stackpanel
            double canvasHeight = ProductLayerCanvas.ActualHeight;
            if (canvasHeight == 0 && StationStackPanel.ActualHeight > 0) canvasHeight = StationStackPanel.ActualHeight;
            if (canvasHeight == 0) canvasHeight = 100; // Fallback

            double targetX = _stationCenterXVals[0] - (visualWidth / 2.0);
            double targetY = (canvasHeight - visualHeight) / 2.0; // Center vertically

            if (animateFromEntryPoint)
            {
                Canvas.SetLeft(productVisual, -visualWidth); // Start off-screen to the left
                Canvas.SetTop(productVisual, targetY);
                AnimateProductToPosition(productVisual, targetX, 1.0); // Animate to first station's X
            }
            else
            {
                Canvas.SetLeft(productVisual, targetX);
                Canvas.SetTop(productVisual, targetY);
            }
        }

        private void SimulationTimer_Tick(object sender, EventArgs e)
        {
            if (_stationCenterXVals.Count == 0 && _stations.Count > 0)
            {
                CalculateStationPositions();
                if (_stationCenterXVals.Count == 0) return;
            }

            for (int i = _activeProductsData.Count - 1; i >= 0; i--)
            {
                var productData = _activeProductsData[i];
                if (!_productVisuals.TryGetValue(productData.ProductId, out ProductVisualControl productVisual))
                    continue; // Visual not found, skip

                int currentStationIdx = productData.CurrentStationIndex;
                if (currentStationIdx < 0 || currentStationIdx >= _stations.Count) continue;

                StationControl currentStation = _stations[currentStationIdx];

                // Simulate "work done" - specific logic for inspection stations
                if (currentStation.IsTestStationType && productData.CurrentTestResult == TestResultStatus.Testing)
                {
                    // Example: Camera (Station 55, index 55-27 = 28)
                    if (currentStation.StationNumber == "55")
                    { // Camera
                        productData.ProductCode = "SCANNED-" + productData.ProductCode.Replace("SCANNED-", ""); // Simulate scan
                    }
                    // Simulate other tests
                    productData.CurrentTestResult = (new Random().Next(0, 10) < 9) ? TestResultStatus.OK : TestResultStatus.NG; // Higher OK rate
                    currentStation.ProductStatusAtStation = productData.CurrentTestResult;
                    currentStation.UpdateStationDisplayBrush();
                }
                else if (currentStation.StationType == StationType.WorkerRoller && productData.CurrentTestResult != TestResultStatus.OK)
                { // Assume worker completes task
                    productData.CurrentTestResult = TestResultStatus.OK; // Simplified
                }


                int nextStationIdx = currentStationIdx + 1;

                if (nextStationIdx < _stations.Count)
                {
                    StationControl nextStation = _stations[nextStationIdx];
                    if (!nextStation.HasProduct)
                    {
                        currentStation.HasProduct = false;
                        if (currentStation.IsTestStationType) currentStation.ProductStatusAtStation = TestResultStatus.None;
                        currentStation.UpdateStationDisplayBrush();

                        productData.CurrentStationIndex = nextStationIdx;
                        nextStation.HasProduct = true;
                        if (nextStation.IsTestStationType)
                        {
                            productData.CurrentTestResult = TestResultStatus.Testing;
                            nextStation.ProductStatusAtStation = productData.CurrentTestResult;
                        }
                        nextStation.UpdateStationDisplayBrush();
                        AnimateProductToStation(productVisual, nextStationIdx);
                    }
                }
                else // Product at the last station of inspection line
                {
                    currentStation.HasProduct = false;
                    if (currentStation.IsTestStationType) currentStation.ProductStatusAtStation = TestResultStatus.None;
                    currentStation.UpdateStationDisplayBrush();
                    AnimateProductOffLine(productVisual, productData);
                }
            }
        }

        private void AnimateProductToPosition(ProductVisualControl visual, double targetX, double durationSeconds)
        {
            if (visual == null) return;
            var animation = new DoubleAnimation
            {
                To = targetX,
                Duration = TimeSpan.FromSeconds(durationSeconds),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
            };
            Storyboard.SetTarget(animation, visual);
            Storyboard.SetTargetProperty(animation, new PropertyPath(Canvas.LeftProperty));
            var sb = new Storyboard();
            sb.Children.Add(animation);
            sb.Begin();
        }

        private void AnimateProductToStation(ProductVisualControl visual, int stationIndex)
        {
            if (stationIndex < 0 || stationIndex >= _stationCenterXVals.Count || visual == null) return;

            double visualWidth = visual.DesiredSize.Width;
            if (visualWidth == 0)
            {
                visual.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                visualWidth = visual.DesiredSize.Width;
            }
            if (visualWidth == 0) visualWidth = 70; // Fallback

            double targetX = _stationCenterXVals[stationIndex] - (visualWidth / 2.0);
            AnimateProductToPosition(visual, targetX, 0.8); // 0.8s for inter-station animation
        }

        private void AnimateProductOffLine(ProductVisualControl visual, ProductItem productData)
        {
            if (visual == null) return;
            double targetX = ProductLayerCanvas.ActualWidth + visual.ActualWidth + 50; // Move it off to the right

            var animation = new DoubleAnimation
            {
                To = targetX,
                Duration = TimeSpan.FromSeconds(1.0),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseInOut }
            };

            Storyboard.SetTarget(animation, visual);
            Storyboard.SetTargetProperty(animation, new PropertyPath(Canvas.LeftProperty));

            var sb = new Storyboard();
            sb.Children.Add(animation);
            sb.Completed += (s, e) =>
            {
                ProductLayerCanvas.Children.Remove(visual);
                if (productData != null)
                {
                    _productVisuals.Remove(productData.ProductId);
                    _activeProductsData.Remove(productData);
                }
                System.Diagnostics.Debug.WriteLine($"Product {productData?.ProductId} completed Inspection Line and removed.");
            };
            sb.Begin();
        }
    }
}