﻿<Window x:Class="ZoomableApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ZoomableApp"
        xmlns:controls="clr-namespace:ZoomableApp.SharedControls"
        xmlns:converters="clr-namespace:ZoomableApp.Converters"
        xmlns:ViewModels="clr-namespace:ZoomableApp.ViewModels"
        xmlns:views="clr-namespace:ZoomableApp.Views"
        mc:Ignorable="d"
        Title="MainWindow" Height="700" Width="800"
        x:Name="mainWindow">
    <Window.Resources>
        <!-- Bell Shake Animation -->
        <Storyboard x:Key="BellShakeStoryboard" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Angle">
                <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0"/>
                <!-- Pause -->
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
        <Storyboard x:Key="ResetBellAngleStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Angle" To="0" Duration="0:0:0.01"/>
        </Storyboard>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:StringContainsFailedConverter x:Key="StringContainsFailedConverter"/>
        <converters:PlanItemStatusToColorConverter x:Key="StatusToColorConverter"/>
        <converters:PlanItemStatusToTextColorConverter x:Key="StatusToTextColorConverter"/>
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="15">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#CC0000"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <!-- Sidebar Column -->
            <ColumnDefinition x:Name="SidebarColumn" Width="Auto"/>
            <!-- Default to Auto (collapsed) -->
            <!-- Main Content Column -->
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar Menu (Grid.Column="0") -->
        <Border Grid.Column="0" Background="#2C3E50" BorderBrush="#222" BorderThickness="0,0,1,0">
            <Grid x:Name="SidebarPanel" Visibility="Collapsed">
                <!-- Close Button - Absolute positioned -->
                <Button HorizontalAlignment="Right" VerticalAlignment="Top"
                        Margin="0,10,10,0" Style="{StaticResource CloseButtonStyle}"
                        Click="CloseSidebarButton_Click" Cursor="Hand" ToolTip="Đóng menu"
                        Panel.ZIndex="10">
                    <Path Data="M18,6L6,18M6,6L18,18" Stroke="White" StrokeThickness="2"
                          Width="16" Height="16" Stretch="Uniform"/>
                </Button>

                <!-- User Info Section - Absolute positioned -->
                <Border HorizontalAlignment="Stretch" VerticalAlignment="Top"
                        Background="#34495E" Padding="15" Margin="0,50,0,0">
                    <StackPanel Orientation="Horizontal">
                        <!-- Avatar -->
                        <Border Width="50" Height="50" CornerRadius="25" Background="#95A5A6"
                                VerticalAlignment="Top" Margin="0,0,15,0">
                            <Path Data="M12,12c2.21,0 4,-1.79 4,-4s-1.79,-4 -4,-4 -4,1.79 -4,4 1.79,4 4,4zM12,14c-2.67,0 -8,1.34 -8,4v2h16v-2c0,-2.66 -5.33,-4 -8,-4z"
                                  Fill="White" Stretch="Uniform" Width="30" Height="30"/>
                        </Border>

                        <!-- User Details -->
                        <StackPanel VerticalAlignment="Top">
                            <TextBlock x:Name="UserFullNameTextBlock" Text="Chưa đăng nhập"
                                       Foreground="White" FontWeight="Bold" FontSize="14"
                                       TextWrapping="Wrap"/>
                            <TextBlock x:Name="UserShiftTextBlock" Text=""
                                       Foreground="#BDC3C7" FontSize="11" Margin="0,3,0,0"
                                       TextWrapping="Wrap" LineHeight="14"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Menu Items - Centered in entire sidebar -->
                <ListBox x:Name="SidebarMenuListBox" Background="Transparent" BorderThickness="0"
                         SelectionMode="Single" Foreground="White" FontSize="16"
                         SelectionChanged="SidebarMenuListBox_SelectionChanged"
                         VerticalAlignment="Center" HorizontalAlignment="Stretch"
                         Margin="0">
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Padding" Value="15,12"/>
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#3498DB"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#2980B9"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </ListBox.ItemContainerStyle>
                    <ListBoxItem Content="🏠 Trang chủ"/>
                    <ListBoxItem Content="📋 Kế hoạch"/>
                    <ListBoxItem Content="📊 Báo cáo"/>
                    <ListBoxItem Content="🔧 Bảo dưỡng"/>
                    <ListBoxItem Content="👤 Tài khoản"/>
                </ListBox>

                <!-- Logout Button - Absolute positioned at bottom -->
                <Button x:Name="LogoutButton" Content="🚪 Đăng xuất"
                        Background="#E74C3C" Foreground="White" BorderThickness="0"
                        Margin="15,0,15,15" Padding="12" FontSize="14" FontWeight="Bold"
                        Click="LogoutButton_Click" Cursor="Hand"
                        HorizontalAlignment="Stretch" VerticalAlignment="Bottom">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#E74C3C"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#C0392B"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <!-- Header cố định -->
                <RowDefinition Height="*"/>
                <!-- Vùng nội dung chính -->
            </Grid.RowDefinitions>

            <!-- Header cố định -->
            <Grid Grid.Row="0" Background="#F5F5F5" Height="50">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Nút mở sidebar -->
                <Button x:Name="OpenSidebarButton" Grid.Column="0" Content="≡" Width="40" Height="40" FontSize="20" FontWeight="Bold"
                        Background="Transparent" BorderThickness="0" Foreground="#333" VerticalAlignment="Center" Margin="10,0,0,0"
                        Click="OpenSidebarButton_Click" Cursor="Hand"
                        Visibility="{Binding IsSidebarOpen, ElementName=mainWindow, Converter={StaticResource InverseBooleanToVisibilityConverter}}"/>

                <!-- Tên trang ở giữa -->
                <TextBlock x:Name="PageTitleTextBlock" Grid.Column="1" Text="Trang chủ" FontSize="18" FontWeight="Bold"
                           Foreground="#333" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <!-- Bell notification ở bên phải -->
                <Grid Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                    <Button x:Name="ErrorBellButton" Width="40" Height="40" Background="Transparent"
                BorderThickness="0" Click="ErrorBellButton_Click" ToolTip="View Errors">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Grid>
                                    <Path x:Name="BellPath"
                              Data="M12,22c1.1,0 2,-0.9 2,-2h-4c0,1.1 0.9,2 2,2z M18,16v-5c0,-3.07 -1.63,-5.64 -4.5,-6.32L13.5,4c0,-0.83 -0.67,-1.5 -1.5,-1.5s-1.5,0.67 -1.5,1.5v0.68C7.63,5.36 6,7.92 6,11v5l-2,2v1h16v-1l-2,-2z M16,17H8v-6c0,-2.48 1.51,-4.5 4,-4.5s4,2.02 4,4.5V17z"
                              Fill="DimGray" Stretch="Uniform" RenderTransformOrigin="0.5,0.1">
                                        <Path.RenderTransform>
                                            <RotateTransform x:Name="BellButtonTransform" Angle="0"/>
                                        </Path.RenderTransform>
                                    </Path>
                                </Grid>

                                <ControlTemplate.Triggers>
                                    <DataTrigger Binding="{Binding HasNewErrors, ElementName=mainWindow}" Value="True">
                                        <Setter TargetName="BellPath" Property="Fill" Value="Red"/>
                                        <DataTrigger.EnterActions>
                                            <BeginStoryboard x:Name="BellShakeBeginStoryboard">
                                                <!-- Use an inline Storyboard -->
                                                <BeginStoryboard.Storyboard>
                                                    <Storyboard RepeatBehavior="Forever">
                                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="BellPath"
                                                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)">
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0"/>
                                                            <!-- Pause -->
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </BeginStoryboard.Storyboard>
                                            </BeginStoryboard>
                                        </DataTrigger.EnterActions>
                                        <DataTrigger.ExitActions>
                                            <StopStoryboard BeginStoryboardName="BellShakeBeginStoryboard"/>
                                            <BeginStoryboard x:Name="BellResetAngleBeginStoryboard">
                                                <!-- Use an inline Storyboard -->
                                                <BeginStoryboard.Storyboard>
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="BellPath"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                                             To="0" Duration="0:0:0.01"/>
                                                    </Storyboard>
                                                </BeginStoryboard.Storyboard>
                                            </BeginStoryboard>
                                        </DataTrigger.ExitActions>
                                    </DataTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- Badge hiển thị số lượng lỗi -->
                    <Border x:Name="ErrorCountBadge" Background="Red" CornerRadius="8"
                Height="16" MinWidth="16" Padding="3,0"
                HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,-5,-5,0"
                Visibility="{Binding HasNewErrors, ElementName=mainWindow, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding NewErrorCount, ElementName=mainWindow}" Foreground="White"
                       FontSize="10" FontWeight="Bold"
                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </Grid>
            </Grid>

            <!-- Hàng dưới: Vùng nội dung chính -->
            <Grid Grid.Row="1" x:Name="MainContentGrid">
                <!-- Trang Zoom/Pan (mặc định) -->
                <Grid x:Name="ZoomPanPage" Background="White" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <!-- Zoom/Pan area - giảm xuống 1/2 -->
                        <RowDefinition Height="*"/>
                        <!-- Daily Plan area -->
                    </Grid.RowDefinitions>

                    <!-- Zoom/Pan Area -->
                    <Grid Grid.Row="0" Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <!-- Controls -->
                            <RowDefinition Height="*"/>
                            <!-- Viewport -->
                        </Grid.RowDefinitions>

                        <!-- Layout controls -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="Chọn Layout:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <ComboBox x:Name="LayoutSelectorComboBox" Width="200" SelectionChanged="LayoutSelectorComboBox_SelectionChanged">
                                <ComboBoxItem Content="Mainline"/>
                                <ComboBoxItem Content="Inspection"/>
                            </ComboBox>
                            <Button Content="Reset Zoom/Pan" Margin="20,0,0,0" Click="ResetButton_Click"/>

                            <!-- Hiển thị thông tin zoom -->
                            <TextBlock VerticalAlignment="Center" Margin="20,0,0,0">
                                <Run Text="Zoom: "/>
                                <Run Text="{Binding ElementName=ViewScaleTransform, Path=ScaleX, StringFormat={}{0:F2}x}"/>
                            </TextBlock>
                        </StackPanel>

                        <!-- Viewport -->
                        <Border Grid.Row="1" x:Name="ViewportBorder"
                            BorderBrush="DarkRed" BorderThickness="2"
                            Background="LightSteelBlue"
                            ClipToBounds="True"
                            MouseWheel="ViewportBorder_MouseWheel"
                            MouseDown="ViewportBorder_MouseDown"
                            MouseMove="ViewportBorder_MouseMove"
                            MouseUp="ViewportBorder_MouseUp">

                            <!-- Vùng nội dung sẽ được zoom/pan -->
                            <Canvas x:Name="ZoomPanCanvas"
                                Width="2500" Height="300"
                                Background="AliceBlue">
                                <Canvas.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform x:Name="ViewScaleTransform" ScaleX="1" ScaleY="1"/>
                                        <TranslateTransform x:Name="ViewTranslateTransform" X="0" Y="0"/>
                                    </TransformGroup>
                                </Canvas.RenderTransform>
                                <!-- NỘI DUNG SẼ ĐƯỢC TẢI ĐỘNG Ở ĐÂY -->
                            </Canvas>
                        </Border>
                    </Grid>

                    <!-- Daily Plan Section -->
                    <Grid Grid.Row="1" Margin="10,0,10,10" x:Name="DailyPlanSection">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <!-- Header -->
                            <RowDefinition Height="*"/>
                            <!-- DataGrid -->
                        </Grid.RowDefinitions>

                        <!-- Header với title và controls -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Title -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Text="KẾ HOẠCH" FontSize="18" FontWeight="Bold" Foreground="#2C3E50" Margin="0,0,10,0"/>
                                <TextBlock x:Name="TodayDateText" Text="{Binding TodayDateText}" FontSize="16" Foreground="#7F8C8D" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Current Product Display -->
                            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="SẢN PHẨM TIẾP THEO" FontSize="14" FontWeight="Bold" Foreground="#34495E" Margin="0,0,10,0"/>
                                <Border Background="#ECF0F1" CornerRadius="4" Padding="8,4">
                                    <TextBlock x:Name="CurrentProductText" Text="{Binding CurrentProductText}" FontSize="16" FontWeight="Bold"
                                              Foreground="#E74C3C"/>
                                </Border>
                            </StackPanel>

                            <!-- Control Buttons -->
                            <Button Grid.Column="2" x:Name="RefreshPlanButton" Width="35" Height="35" Margin="5,0"
                                    Background="#3498DB" BorderThickness="0" ToolTip="Cập nhật kế hoạch"
                                    Command="{Binding RefreshCommand}">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="17" BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                                <Path Data="M17.65,6.35C16.2,4.9 14.21,4 12,4c-4.42,0 -7.99,3.58 -7.99,8s3.57,8 7.99,8c3.73,0 6.84,-2.55 7.73,-6h-2.08c-0.82,2.33 -3.04,4 -5.65,4 -3.31,0 -6,-2.69 -6,-6s2.69,-6 6,-6c1.66,0 3.14,0.69 4.22,1.78L13,11h7V4L17.65,6.35z"
                                      Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                            </Button>

                            <Button Grid.Column="3" x:Name="MarkCompleteButton" Width="35" Height="35" Margin="5,0"
                                    Background="#27AE60" BorderThickness="0" ToolTip="Hoàn thành sản phẩm hiện tại"
                                    Command="{Binding MarkCompleteCommand}">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="17" BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                                <Path Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                      Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                            </Button>

                            <Button Grid.Column="4" x:Name="StartNextButton" Width="35" Height="35" Margin="5,0"
                                    Background="#F39C12" BorderThickness="0" ToolTip="Bắt đầu sản phẩm được chọn"
                                    Command="{Binding StartNextCommand}">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" CornerRadius="17" BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Button.Template>
                                <Path Data="M8,5.14V19.14L19,12.14L8,5.14Z"
                                      Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                            </Button>
                        </Grid>

                        <!-- DataGrid for Daily Plan -->
                        <DataGrid Grid.Row="1" x:Name="DailyPlanDataGrid"
                                  ItemsSource="{Binding DailyPlanItems}"
                                  SelectedItem="{Binding SelectedItem, Mode=TwoWay}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeRows="False"
                                  IsReadOnly="True"
                                  SelectionMode="Single"
                                  GridLinesVisibility="All"
                                  HeadersVisibility="Column"
                                  RowHeaderWidth="0"
                                  FontSize="14"
                                  MinHeight="200"
                                  HorizontalScrollBarVisibility="Auto"
                                  VerticalScrollBarVisibility="Auto">

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Background" Value="{Binding Status, Converter={StaticResource StatusToColorConverter}}"/>
                                    <Setter Property="Foreground" Value="{Binding Status, Converter={StaticResource StatusToTextColorConverter}}"/>
                                    <Setter Property="Height" Value="40"/>
                                    <Style.Triggers>
                                        <!-- Prevent selection for completed and in-progress items -->
                                        <DataTrigger Binding="{Binding Status}" Value="Completed">
                                            <Setter Property="IsEnabled" Value="False"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="InProgress">
                                            <Setter Property="IsEnabled" Value="False"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="No" Binding="{Binding No}" Width="50" MinWidth="40"/>
                                <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="80" MinWidth="60"/>
                                <DataGridTextColumn Header="Model name" Binding="{Binding ModelName}" Width="150" MinWidth="120"/>
                                <DataGridTextColumn Header="Market" Binding="{Binding Market}" Width="100" MinWidth="80"/>
                                <DataGridTextColumn Header="Q'ty" Binding="{Binding Quantity}" Width="60" MinWidth="50"/>
                                <DataGridTextColumn Header="Start time" Binding="{Binding StartTime}" Width="100" MinWidth="80"/>
                                <DataGridTextColumn Header="Stop time" Binding="{Binding StopTime}" Width="100" MinWidth="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Grid>

                <!-- Trang Kế hoạch -->
                <ContentControl x:Name="PlanPage" Visibility="Collapsed">
                    <views:PlanPage />
                </ContentControl>

                <!-- Trang Báo cáo -->
                <Grid x:Name="ReportPage" Background="LightGreen" Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="📊 Trang Báo cáo" FontSize="24" FontWeight="Bold" Foreground="DarkGreen" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        <TextBlock Text="Đang phát triển..." FontSize="16" Foreground="Gray" HorizontalAlignment="Center"/>
                        <TextBlock Text="Maintaining..." FontStyle="Italic" Foreground="Orange" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- Trang Bảo dưỡng -->
                <Grid x:Name="MaintenancePage" Background="LightCoral" Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="🔧 Trang Bảo dưỡng" FontSize="24" FontWeight="Bold" Foreground="DarkRed" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        <TextBlock Text="Đang phát triển..." FontSize="16" Foreground="Gray" HorizontalAlignment="Center"/>
                        <TextBlock Text="Maintaining..." FontStyle="Italic" Foreground="Orange" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- Trang Tài khoản -->
                <Grid x:Name="AccountPage" Background="LightGoldenrodYellow" Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="👤 Trang Tài khoản" FontSize="24" FontWeight="Bold" Foreground="DarkGoldenrod" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        <TextBlock Text="Đang phát triển..." FontSize="16" Foreground="Gray" HorizontalAlignment="Center"/>
                        <TextBlock Text="Maintaining..." FontStyle="Italic" Foreground="Orange" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>
            </Grid>



            <!-- Error List Popup -->
            <Popup x:Name="ErrorListPopup" Placement="Bottom" PlacementTarget="{Binding ElementName=ErrorBellButton}"
               StaysOpen="False" AllowsTransparency="True" PopupAnimation="Slide"
               IsOpen="{Binding IsErrorListPopupOpen, ElementName=mainWindow, Mode=TwoWay}">
                <Border Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1" CornerRadius="3"
                    Padding="10" MaxHeight="300" MinWidth="350" MaxWidth="500">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="Error Notifications" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <ListBox Grid.Row="1" x:Name="ErrorListBox" ItemsSource="{Binding AllErrors, ElementName=mainWindow}"
                             ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemContainerStyle>
                                <Style TargetType="ListBoxItem">
                                    <Setter Property="Padding" Value="3"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsNew}" Value="True">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ListBox.ItemContainerStyle>
                        </ListBox>
                        <Button Grid.Row="2" Content="Clear All Errors (Display Only)" Click="ClearAllErrorsButton_Click" Margin="0,10,0,0" HorizontalAlignment="Right" Padding="5,2"/>
                    </Grid>
                </Border>
            </Popup>



            <!-- Toast Notification Area (Bottom Right) -->
            <Grid x:Name="ToastNotificationContainer" Panel.ZIndex="1000" VerticalAlignment="Bottom" HorizontalAlignment="Right"/>
        </Grid>
    </Grid>
</Window>
