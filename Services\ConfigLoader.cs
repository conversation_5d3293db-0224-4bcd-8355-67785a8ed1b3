﻿using System.IO;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ZoomableApp.PLC;
using Microsoft.Extensions.Configuration;

namespace ZoomableApp.Services
{
    public class PlcConfigFileEntry
    {
        public string Id { get; set; }
        public string Name { get; set; } // Tên hiển thị
        public string IpAddress { get; set; }
        //public int LogicalStationNumber { get; set; }
        public int Port { get; set; }
        public bool AutoConnect { get; set; } = true; // Mặc định là tự kết nối
        public bool IsEnabled { get; set; } = true;   // Mặc định là sử dụng
    }

    public static class ConfigLoader
    {
        private const string ConfigFileName = "plc_configs.json";

        public static List<PlcConnectionInfo> LoadPlcConfigs()
        {
            var plcInfos = new List<PlcConnectionInfo>();
            string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ConfigFileName);
            // Hoặc nếu file nằm trong thư mục con:
            // string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs", ConfigFileName);


            if (!File.Exists(filePath))
            {
                Debug.WriteLine($"ConfigLoader: PLC config file not found at '{filePath}'. Returning empty list.");
                // Có thể tạo file mẫu nếu không tồn tại
                CreateSampleConfigFile(filePath);
                return plcInfos;
            }

            try
            {
                string jsonString = File.ReadAllText(filePath);
                var configEntries = JsonSerializer.Deserialize<List<PlcConfigFileEntry>>(jsonString, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (configEntries != null)
                {
                    foreach (var entry in configEntries)
                    {
                        if (entry.IsEnabled) // Chỉ load những PLC được enable
                        {
                            plcInfos.Add(new PlcConnectionInfo
                            {
                                Id = entry.Id,
                                Name = entry.Name, // PlcConnectionInfo chưa có Name, cần thêm nếu muốn
                                IpAddress = entry.IpAddress,
                                Port = entry.Port,
                                //LogicalStationNumber = entry.LogicalStationNumber,

                                ConnectionStatus = "Not Connected", // Trạng thái ban đầu
                                AutoConnectOnStartup = entry.AutoConnect
                            });
                        }
                    }
                }
                Debug.WriteLine($"ConfigLoader: Loaded {plcInfos.Count} enabled PLC configurations from '{filePath}'.");
            }
            catch (JsonException jsonEx)
            {
                Debug.WriteLine($"ConfigLoader: Error deserializing PLC config file '{filePath}'. Error: {jsonEx.Message}");
                // Xử lý lỗi (ví dụ: thông báo cho người dùng, sử dụng cấu hình mặc định,...)
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ConfigLoader: Error reading PLC config file '{filePath}'. Error: {ex.Message}");
            }

            return plcInfos;
        }

        private static void CreateSampleConfigFile(string filePath)
        {
            // Tạo file mẫu nếu muốn
            var sampleEntries = new List<PlcConfigFileEntry>
            {
                new PlcConfigFileEntry { Id = "PLC1_Sample", Name="PLC Mẫu 1", IpAddress = "*************", Port = 5007, AutoConnect = true, IsEnabled = true },
                new PlcConfigFileEntry { Id = "PLC2_Sample", Name="PLC Mẫu 2", IpAddress = "*************", Port = 5007, AutoConnect = false, IsEnabled = true }
            };
            try
            {
                string jsonString = JsonSerializer.Serialize(sampleEntries, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(filePath, jsonString);
                Debug.WriteLine($"ConfigLoader: Created sample PLC config file at '{filePath}'.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ConfigLoader: Error creating sample PLC config file. Error: {ex.Message}");
            }
        }

        public static ExcelSettings LoadExcelSettings()
        {
            try
            {
                var builder = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                var configuration = builder.Build();
                var excelSettings = new ExcelSettings();
                configuration.GetSection("ExcelSettings").Bind(excelSettings);

                Debug.WriteLine($"Excel file path from config: {excelSettings.PlanFilePath}");
                return excelSettings;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading Excel settings: {ex.Message}");
                return new ExcelSettings { PlanFilePath = "E:\\Project-Dat\\test.xlsx" }; // Default fallback
            }
        }
    }

    public class ExcelSettings
    {
        public string PlanFilePath { get; set; } = string.Empty;
    }
}
